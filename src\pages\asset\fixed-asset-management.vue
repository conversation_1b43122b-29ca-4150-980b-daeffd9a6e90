<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">固定资产管理</h1>
      <p class="page-subtitle">固定资产登记与管理</p>
    </div>
    
    <div class="page-content">
      <div class="content-grid">
        <!-- 资产统计卡片 -->
        <div class="stat-card">
          <div class="stat-icon">
            <div class="i-mdi-office-building text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">¥2.8M</div>
            <div class="stat-label">总资产价值</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon good">
            <div class="i-mdi-check-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">89</div>
            <div class="stat-label">正常资产</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon warning">
            <div class="i-mdi-alert-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">12</div>
            <div class="stat-label">待维护</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon depreciation">
            <div class="i-mdi-trending-down text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">¥180K</div>
            <div class="stat-label">年折旧额</div>
          </div>
        </div>
      </div>
      
      <!-- 资产列表 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>固定资产列表</h3>
          <div class="panel-actions">
            <button class="btn-primary">新增资产</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>资产编号</th>
                  <th>资产名称</th>
                  <th>资产类别</th>
                  <th>购置日期</th>
                  <th>原值</th>
                  <th>净值</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="asset in assetList" :key="asset.id">
                  <td>{{ asset.code }}</td>
                  <td>{{ asset.name }}</td>
                  <td>{{ asset.category }}</td>
                  <td>{{ asset.purchaseDate }}</td>
                  <td>¥{{ asset.originalValue.toLocaleString() }}</td>
                  <td>¥{{ asset.netValue.toLocaleString() }}</td>
                  <td>
                    <span class="status-badge" :class="asset.status">
                      {{ asset.statusText }}
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">查看</button>
                      <button class="btn-small">编辑</button>
                      <button class="btn-small btn-danger">报废</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface FixedAsset {
  id: number
  code: string
  name: string
  category: string
  purchaseDate: string
  originalValue: number
  netValue: number
  status: string
  statusText: string
}

const assetList = ref<FixedAsset[]>([
  {
    id: 1,
    code: 'FA001',
    name: '养殖池建设工程',
    category: '建筑物',
    purchaseDate: '2023-01-15',
    originalValue: 800000,
    netValue: 720000,
    status: 'good',
    statusText: '正常'
  },
  {
    id: 2,
    code: 'FA002',
    name: '水处理设备系统',
    category: '机器设备',
    purchaseDate: '2023-03-20',
    originalValue: 450000,
    netValue: 380000,
    status: 'good',
    statusText: '正常'
  },
  {
    id: 3,
    code: 'FA003',
    name: '办公楼装修',
    category: '装修工程',
    purchaseDate: '2022-12-10',
    originalValue: 320000,
    netValue: 250000,
    status: 'warning',
    statusText: '待维护'
  },
  {
    id: 4,
    code: 'FA004',
    name: '监控系统设备',
    category: '电子设备',
    purchaseDate: '2023-05-08',
    originalValue: 180000,
    netValue: 160000,
    status: 'good',
    statusText: '正常'
  }
])
</script>

<style scoped>
.page-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(102, 204, 255, 0.1);
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 204, 255, 0.2);
  color: #66ccff;
}

.stat-icon.good {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-icon.warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.stat-icon.depreciation {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #66ccff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(102, 204, 255, 0.7);
}

.data-panel {
  background: rgba(102, 204, 255, 0.05);
  border: 1px solid rgba(102, 204, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #66ccff;
}

.btn-primary {
  background: linear-gradient(135deg, #66ccff, #33aaff);
  color: #001122;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 204, 255, 0.3);
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(102, 204, 255, 0.1);
}

.data-table th {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  font-weight: 600;
}

.data-table td {
  color: rgba(102, 204, 255, 0.9);
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.good {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 12px;
  border: 1px solid rgba(102, 204, 255, 0.3);
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: rgba(102, 204, 255, 0.2);
}

.btn-small.btn-danger {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.btn-small.btn-danger:hover {
  background: rgba(239, 68, 68, 0.2);
}
</style>
