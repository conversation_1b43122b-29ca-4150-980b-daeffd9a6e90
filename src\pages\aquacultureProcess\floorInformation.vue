<template>
  <div class="flex bg-gray-50 text-dark">
    <!-- 右侧内容 B列 - 添加滚动容器 -->
    <div class="flex-1 h-full overflow-hidden ml-[2.5%] mr-[2.5%]">
      <!-- 内容滚动容器 -->
      <div class="h-full overflow-y-hidden pr-2" style="scrollbar-width: thin;">
        <div class="pb-2">
          <!-- 顶部 Brow1：养殖池子图示区域 -->
          <div class="h-[32vh] bg-white rounded-xl shadow-sm p-4 mt-1 overflow-hidden flex flex-col">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-lg font-semibold flex items-center">
                <i class="fa fa-th-large text-primary mr-2"></i>
                养殖池子监控
              </h2>
              <div class="flex items-center space-x-3">
                <div class="flex items-center text-sm">
                  <span class="w-3 h-3 rounded-full bg-green mr-2"></span>
                  <span>正常</span>
                </div>
                <div class="flex items-center text-sm">
                  <span class="w-3 h-3 rounded-full bg-orange mr-2"></span>
                  <span>预警</span>
                </div>
                <div class="flex items-center text-sm">
                  <span class="w-3 h-3 rounded-full bg-red mr-2"></span>
                  <span>靠警</span>
                </div>
                <div class="flex items-center text-sm">
                  <span class="w-3 h-3 rounded-full  mr-2"></span>
                </div>
              </div>
            </div>
            
            <!-- 4×9 圆点阵列 - 添加水平滚动支持 -->
            <div class="flex-1 overflow-hidden">
              <div class="h-full overflow-x-auto">
                <div id="tank-grid" class="h-full w-full grid grid-cols-9 grid-rows-4 gap-2 p-2">
                  <div v-for="tank in tanks" :key="tank.id" 
                    :class="['tank-dot', `tank-dot-${tank.status}`, 'scale-hover', 
                            { 'tank-selected': selectedTankId === tank.id }]"
                    title="`养殖池 ${tank.id} - 第${tankProgress[tank.id]?.actualProgress || 45}天`"
                    @click="selectTank(tank.id)"
                    class="w-8 h-8 min-w-18 min-h-18">
                    <span class="text-3xl font-medium absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2" 
                          :style="{ color: getStatusColor(tank.status) }">
                      {{ tank.id }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 中间 Brow2：数据切换导航 → 拖动进度条 -->
          <div class="h-[10vh] flex items-center justify-center my-1">
            <div class="w-full relative">
              <div class="mt-4 text-center" style="margin-bottom:10px">
                <span class="text-lg font-semibold text-primary">
                  {{ selectedTankId ? `池子 ${selectedTankId} 当前进度：第${sliderValue}天` : '请选择一个养殖池' }}
                  <span v-if="showRestoreFeedback" class="restore-feedback text-green-500 ml-2">
                    <i class="fa fa-check"></i> 已恢复
                  </span>
                </span>
              </div>
              <div class="flex items-center gap-6">
                <span class="text-base text-gray-600 w-16 text-right font-medium">{{ days[0].label }}</span>
                <div class="flex-1 relative">
                  <input
                    type="range"
                    min="1"
                    max="100"
                    step="1"
                    v-model.number="sliderValue"
                    @input="onSliderInput"
                    class="w-full slider"
                    :style="{ '--fill': fillPercent, '--progress': progressPercent }"
                    :disabled="!selectedTankId"
                  />
                  <div class="absolute top-8 left-0 right-0 flex justify-between text-xs text-gray-500">
                    <span v-for="day in days" :key="day.id">{{ day.label }}</span>
                  </div>
                </div>
                <span class="text-base text-gray-600 w-16 font-medium">{{ days[days.length - 1].label }}</span>
              </div>
              <!-- 恢复按钮 -->
              <button 
                @click="resetToProgress" 
                class="absolute top-0 right-0 text-blue-500 bg-primary px-3 py-1 rounded-lg text-sm hover:bg-primary/80 transition-colors flex items-center gap-1"
                :title="selectedTankId ? `恢复到池子 ${selectedTankId} 的实际养殖进度` : '恢复进度'"
                :disabled="!selectedTankId"
                :class="{ 'restore-button-animate': showRestoreFeedback }"
              >
                <i class="fa fa-refresh"></i>
                恢复
              </button>
            </div>
          </div>
          
          <!-- 底部 Brow3：内容显示区域 - 改为上下布局 -->
          <div class="h-[43vh] flex flex-col gap-2 mb-1">
            <!-- 上部分：监控区域和饲料使用情况 -->
            <div class="flex-1 flex gap-2">
              <!-- 左侧：监控区域（横向合并） -->
              <div class="w-1/2 bg-white rounded-xl shadow-sm p-3 overflow-hidden">
                <h3 class="text-sm font-medium mb-2 flex items-center">
                  <i class="fa fa-video-camera text-primary mr-2"></i>
                  监控区域
                </h3>
                <div class="w-full h-[calc(100%-20px)] flex gap-2">
                  <!-- 监控区域1 -->
                  <div class="flex-1 bg-gray-100 rounded-lg overflow-hidden relative">
                    <img src="@/assets/images/chizi1.png" alt="养殖池监控画面" class="w-full h-full object-cover">
                    <div class="absolute top-2 right-2 bg-primary/80 text-white text-xs px-2 py-1 rounded-full flex items-center">
                      <span class="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
                      监控1
                    </div>
                  </div>
                  
                  <!-- 监控区域2 -->
                  <div class="flex-1 bg-gray-100 rounded-lg overflow-hidden relative">
                    <img src="@/assets/images/chizi2.png" alt="养殖池监控画面" class="w-full h-full object-cover">
                    <div class="absolute top-2 right-2 bg-primary/80 text-white text-xs px-2 py-1 rounded-full flex items-center">
                      <span class="w-2 h-2 bg-white rounded-full mr-1 animate-pulse"></span>
                      监控2
                    </div>
                  </div>
                </div>
              </div>
    
    <!-- 右侧：饲料使用情况 -->
      <div class="w-1/2 bg-white rounded-xl shadow-sm p-3 overflow-y-auto">
          <h3 class="text-sm font-medium mb-2 flex items-center">
            <i class="fa fa-cutlery text-primary mr-2"></i>
            饲料使用情况
          </h3>
          <div class="space-y-2">
            <div class="flex justify-between items-center p-1 border-b border-gray-100">
              <span class="text-gray-700 text-sm">批次编号</span>
              <span class="font-medium text-sm">{{ selectedTankId ? feedInfo.batchNumber : '--' }}</span>
            </div>
            <div class="flex justify-between items-center p-1 border-b border-gray-100">
              <span class="text-gray-700 text-sm">投喂量</span>
              <span class="font-medium text-sm">{{ selectedTankId ? feedInfo.feedAmount : '--' }}</span>
            </div>
            <div class="flex justify-between items-center p-1 border-b border-gray-100">
              <span class="text-gray-700 text-sm">投喂时间</span>
              <span class="font-medium text-sm">{{ selectedTankId ? feedInfo.feedTime : '--' }}</span>
            </div>
            <div class="flex justify-between items-center p-1">
              <span class="text-gray-700 text-sm">剩余量</span>
              <span class="font-medium text-sm text-success">{{ selectedTankId ? feedInfo.remainingAmount : '--' }}</span>
            </div>
          </div>
        </div>
      </div>
  
       <!-- 下部分：5个小K线图 -->
        <div class="flex-1 bg-white rounded-xl shadow-sm p-3 overflow-hidden">
            <h3 class="text-sm font-medium mb-2 flex items-center">
                <i class="fa fa-line-chart text-primary mr-2"></i>
                水质参数监控 - 第{{ sliderValue }}天 24小时变化
            </h3>
            <div class="w-full h-[calc(100%-30px)] grid grid-cols-5 gap-2">
                <!-- 水温图表 -->
                <div class="chart-container">
                    <div class="chart-title">水温 (°C)</div>
                    <canvas ref="tempChart" class="water-quality-chart"></canvas>
                </div>
                
                <!-- PH值图表 -->
                <div class="chart-container">
                    <div class="chart-title">PH值</div>
                    <canvas ref="phChart" class="water-quality-chart"></canvas>
                </div>
                
                <!-- 盐度图表 -->
                <div class="chart-container">
                    <div class="chart-title">盐度 (%)</div>
                    <canvas ref="salinityChart" class="water-quality-chart"></canvas>
                </div>
                
                <!-- 水位图表 -->
                <div class="chart-container">
                    <div class="chart-title">水位 (m)</div>
                    <canvas ref="waterLevelChart" class="water-quality-chart"></canvas>
                </div>
                
                <!-- 溶解氧图表 -->
                <div class="chart-container">
                    <div class="chart-title">溶解氧 (mg/L)</div>
                    <canvas ref="oxygenChart" class="water-quality-chart"></canvas>
                </div>
            </div>
        </div>
      

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, reactive, computed, watch } from 'vue';
import Chart from 'chart.js/auto';

// 图表实例变量
let tempChartInstance = null;
let phChartInstance = null;
let salinityChartInstance = null;
let waterLevelChartInstance = null;
let oxygenChartInstance = null;

export default {
  setup() {
    // 响应式数据
    const buildingOpen = reactive({
      '001': true,
      '002': false,
      '003': false
    });
    
    const activeFloor = ref('001');
    const activeDay = ref('day45');
    const sliderValue = ref(45);
    const selectedTankId = ref(1);
    const showRestoreFeedback = ref(false);
    const restoreFeedbackTimer = ref(null);

    // 水质参数图表引用
    const tempChart = ref(null);
    const phChart = ref(null);
    const salinityChart = ref(null);
    const waterLevelChart = ref(null);
    const oxygenChart = ref(null);
    
    // 为每个池子存储进度数据：实际进度和当前显示进度
    const tankProgress = reactive({});
    
    const fillPercent = computed(() => {
      const ratio = (sliderValue.value - 1) / (100 - 1);
      return `${Math.max(0, Math.min(1, ratio)) * 100}%`;
    });

    const progressPercent = computed(() => {
      if (!selectedTankId.value) return '0%';
      const currentProgress = tankProgress[selectedTankId.value]?.actualProgress || 45;
      const ratio = (currentProgress - 1) / (100 - 1);
      return `${Math.max(0, Math.min(1, ratio)) * 100}%`;
    });
    
    // 水质参数图表引用
    const waterQualityChart = ref(null);
    
    // 水质参数范围定义
    const waterQualityRanges = {
      temperature: { min: 20, max: 29, unit: '°C' },
      ph: { min: 7.3, max: 8.8, unit: '' },
      salinity: { min: 10, max: 26, unit: '%' },
      waterLevel: { min: 0.7, max: 1.45, unit: 'm' },
      oxygen: { min: 5.1, max: 8.5, unit: 'mg/L' }
    };

    // 选中养殖池
    const selectTank = (tankId) => {
      // 如果点击的是已选中的池子，则取消选中
      if (selectedTankId.value === tankId) {
        selectedTankId.value = null;
        sliderValue.value = 45; // 重置为默认值
      } else {
        selectedTankId.value = tankId;
        
        // 如果该池子还没有进度数据，初始化进度
        if (!tankProgress[tankId]) {
          // 为不同池子设置不同的随机进度（30-80天之间）
          const actualProgress = Math.floor(Math.random() * 50) + 30;
          tankProgress[tankId] = {
            actualProgress: actualProgress,
            displayProgress: actualProgress // 初始时显示进度等于实际进度
          };
        }
        
        // 更新滑块值为当前选中池子的显示进度
        sliderValue.value = tankProgress[tankId].displayProgress;
        
        // 更新饲料信息（模拟不同池子的不同饲料信息）
        updateFeedInfo(tankId);
        
        // 更新水质参数图表
        updateWaterQualityCharts(tankProgress[tankId].displayProgress);
        
        console.log(`选中养殖池 ${tankId}，实际进度：第${tankProgress[tankId].actualProgress}天，显示进度：第${tankProgress[tankId].displayProgress}天`);
      }
      
      // 隐藏恢复反馈
      showRestoreFeedback.value = false;
      if (restoreFeedbackTimer.value) {
        clearTimeout(restoreFeedbackTimer.value);
      }
    };
    
    // 更新饲料信息（模拟）
    const updateFeedInfo = (tankId) => {
      // 模拟不同池子的饲料信息
      const feedInfos = {
        1: { batchNumber: '001-ct1-24', feedAmount: '120kg', feedTime: '08:30, 14:30', remainingAmount: '350kg' },
        2: { batchNumber: '002-ct2-24', feedAmount: '95kg', feedTime: '09:00, 15:00', remainingAmount: '280kg' },
        3: { batchNumber: '003-ct3-24', feedAmount: '110kg', feedTime: '08:45, 14:45', remainingAmount: '320kg' },
        // 可以为更多池子添加数据...
      };
      
      // 使用默认数据或特定池子数据
      if (feedInfos[tankId]) {
        Object.assign(feedInfo, feedInfos[tankId]);
      } else {
        // 默认数据
        Object.assign(feedInfo, {
          batchNumber: `${tankId.toString().padStart(3, '0')}-ct${tankId}-24`,
          feedAmount: `${90 + Math.floor(Math.random() * 40)}kg`,
          feedTime: '08:30, 14:30',
          remainingAmount: `${250 + Math.floor(Math.random() * 150)}kg`
        });
      }
    };
    
    const tanks = ref([]);
    
    // 日期数据
    const days = [
      { id: 'day1', label: '第1天' },
      { id: 'day20', label: '第20天' },
      { id: 'day40', label: '第40天' },
      { id: 'day60', label: '第60天' },
      { id: 'day80', label: '第80天' },
      { id: 'day100', label: '第100天' }
    ];
    
    // 饲料信息
    const feedInfo = reactive({
      batchNumber: '001-ct1-24',
      feedAmount: '120kg',
      feedTime: '08:30, 14:30',
      remainingAmount: '350kg'
    });
    
    // 成长趋势图表引用
    const growthChart = ref(null);
    
    // 方法
    const toggleBuildingMenu = (buildingId) => {
      buildingOpen[buildingId] = !buildingOpen[buildingId];
    };
    
    const setActiveFloor = (floorId) => {
      activeFloor.value = floorId;
    };
    
    const setActiveDay = (dayId) => {
      activeDay.value = dayId;
      const dayNumber = parseInt(dayId.replace('day', ''));
      sliderValue.value = dayNumber;
    };

    const onSliderInput = () => {
      if (!selectedTankId.value) return;
      
      const dayNumber = Math.min(Math.max(1, sliderValue.value), 100);
      
      // 更新当前选中池子的显示进度（但不改变实际进度）
      if (tankProgress[selectedTankId.value]) {
        tankProgress[selectedTankId.value].displayProgress = dayNumber;
      }
      
      const dayId = `day${dayNumber}`;
      
      if (dayId !== activeDay.value) {
        activeDay.value = dayId; 
        // 更新水质参数图表
        updateWaterQualityCharts(dayNumber);
        console.log(`池子 ${selectedTankId.value} 显示进度更新为第${dayNumber}天（实际进度：第${tankProgress[selectedTankId.value]?.actualProgress}天）`);
      }
      
      // 隐藏恢复反馈
      showRestoreFeedback.value = false;
      if (restoreFeedbackTimer.value) {
        clearTimeout(restoreFeedbackTimer.value);
      }
    };

    // 更新成长趋势图表
    const updateGrowthChart = (dayNumber) => {
      if (growthChartInstance) {
        const data = getGrowthData(dayNumber);
        growthChartInstance.data.labels = data.labels;
        growthChartInstance.data.datasets[0].data = data.bodyLength;
        growthChartInstance.data.datasets[1].data = data.survivalRate;
        growthChartInstance.update('active');
      }
    };

    const resetToProgress = () => {
      if (!selectedTankId.value || !tankProgress[selectedTankId.value]) return;
      
      // 获取当前选中池子的实际进度
      const actualProgress = tankProgress[selectedTankId.value].actualProgress;
      
      // 恢复到实际进度
      sliderValue.value = actualProgress;
      tankProgress[selectedTankId.value].displayProgress = actualProgress;
      activeDay.value = `day${actualProgress}`;
      
      // 更新图表到实际进度
      updateWaterQualityCharts(actualProgress);
      
      // 显示恢复反馈
      showRestoreFeedback.value = true;
      if (restoreFeedbackTimer.value) {
        clearTimeout(restoreFeedbackTimer.value);
      }
      restoreFeedbackTimer.value = setTimeout(() => {
        showRestoreFeedback.value = false;
      }, 2000);
      
      console.log(`池子 ${selectedTankId.value} 已恢复到实际进度：第${actualProgress}天`);
    };
    
    // 生成养殖池数据
    const generateTankGrid = () => {
      const statuses = ['normal', 'normal', 'normal', 'warning', 'alert'];
      const newTanks = [];
      
      for (let i = 1; i <= 36; i++) {
        // 随机选择状态，大部分为正常
        const randomIndex = Math.random() > 0.9 ? 
                          Math.floor(Math.random() * 2) + 3 : // 10%概率为warning或alert
                          0; // 90%概率为normal
        
        newTanks.push({
          id: i,
          status: statuses[randomIndex]
        });
        
        // 为每个池子初始化进度数据
        if (!tankProgress[i]) {
          const actualProgress = Math.floor(Math.random() * 50) + 30;
          tankProgress[i] = {
            actualProgress: actualProgress,
            displayProgress: actualProgress
          };
        }
      }
      
      tanks.value = newTanks;
    };
    
    // 刷新养殖池数据
    const refreshTankGrid = () => {
      generateTankGrid();
    };
    
    // 获取状态对应的颜色
    const getStatusColor = (status) => {
      switch(status) {
        case 'normal':
          return '#52C41A';
        case 'warning':
          return '#FAAD14';
        case 'alert':
          return '#FF4D4F';
        default:
          return '#52C41A';
      }
    };
    
    // 成长趋势图表实例
    let growthChartInstance = null;
    // 水质参数图表实例
    let waterQualityChartInstance = null;

     // 生成24小时时间标签
    const generateTimeLabels = () => {
      return Array.from({ length: 13 }, (_, i) => {
        return `${i * 2}:00`;
      });
    };
    
    // 获取水质参数数据（根据天数变化）
    const getWaterQualityData = (currentDay) => {
      const timeLabels = generateTimeLabels();
      
      // 基础值（根据天数调整基准）
      const baseValues = {
        temperature: 21 + (currentDay * 0.08) % 4,
        ph: 7.5 + (currentDay * 0.013) % 0.8,
        salinity: 12 + (currentDay * 0.14) % 8,
        waterLevel: 0.8 + (currentDay * 0.0065) % 0.4,
        oxygen: 5.5 + (currentDay * 0.03) % 2
      };
      
      // 生成24小时数据（每2小时一个点）
      const data = {
        labels: timeLabels,
        temperature: [],
        ph: [],
        salinity: [],
        waterLevel: [],
        oxygen: []
      };
      
      // 为每个时间点生成数据
      timeLabels.forEach((_, hourIndex) => {
        // 昼夜效应（白天温度高，晚上温度低）
        const dayNightEffect = hourIndex < 6 || hourIndex > 18 ? -0.5 : 0.5;
        
        // 随机波动
        const randomEffect = () => (Math.random() - 0.5) * 0.8;
        
        // 计算每个参数的值
        data.temperature.push(
          Math.max(waterQualityRanges.temperature.min, 
          Math.min(waterQualityRanges.temperature.max, 
          baseValues.temperature + dayNightEffect + randomEffect()))
        );
        
        data.ph.push(
          Math.max(waterQualityRanges.ph.min, 
          Math.min(waterQualityRanges.ph.max, 
          baseValues.ph + (randomEffect() * 0.1)))
        );
        
        data.salinity.push(
          Math.max(waterQualityRanges.salinity.min, 
          Math.min(waterQualityRanges.salinity.max, 
          baseValues.salinity + (randomEffect() * 0.5)))
        );
        
        data.waterLevel.push(
          Math.max(waterQualityRanges.waterLevel.min, 
          Math.min(waterQualityRanges.waterLevel.max, 
          baseValues.waterLevel + (randomEffect() * 0.05)))
        );
        
        data.oxygen.push(
          Math.max(waterQualityRanges.oxygen.min, 
          Math.min(waterQualityRanges.oxygen.max, 
          baseValues.oxygen + (dayNightEffect * 0.3) + (randomEffect() * 0.2)))
        );
      });
      
      return data;
    };

    // 初始化单个水质参数图表
const initWaterQualityChart = (chartRef, chartInstance, paramName, title, color) => {
  if (chartRef.value) {
    // 销毁现有图表实例（如果存在）
    if (chartInstance) {
      chartInstance.destroy();
    }
    
    const range = waterQualityRanges[paramName];
    
    return new Chart(chartRef.value, {
      type: 'line',
      data: {
        labels: [],
        datasets: [{
          label: title,
          data: [],
          borderColor: color,
          backgroundColor: 'transparent', // 修改为透明背景
          borderWidth: 2,
          fill: false, // 取消填充
          tension: 0.4, // 保持曲线平滑
          pointRadius: 0, // 移除圆点
          pointHoverRadius: 3 // 悬停时显示圆点
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: true,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            mode: 'index',
            intersect: false,
            callbacks: {
              label: function(context) {
                return `${title}: ${context.raw}${range.unit}`;
              }
            }
          }
        },
        scales: {
          x: {
            grid: {
              display: false,
              color: 'rgba(0, 0, 0, 0.1)' // 添加浅色网格线
            },
            ticks: {
              maxRotation: 0,
              autoSkip: true,
              maxTicksLimit: 6,
              color: '#6B7280' // 设置文字颜色
            }
          },
          y: {
            min: range.min - (range.max - range.min) * 0.1,
            max: range.max + (range.max - range.min) * 0.1,
            grid: {
              color: 'rgba(0, 0, 0, 0.1)' // 浅色网格线
            },
            ticks: {
              color: '#6B7280' // 设置文字颜色
            }
          }
        }
      }
    });
  }
  return null;
};

    // 初始化所有水质参数图表
    const initWaterQualityCharts = () => {
      tempChartInstance = initWaterQualityChart(tempChart, tempChartInstance, 'temperature', '水温', 'rgba(255, 99, 132, 1)');
      phChartInstance = initWaterQualityChart(phChart, phChartInstance, 'ph', 'PH值', 'rgba(54, 162, 235, 1)');
      salinityChartInstance = initWaterQualityChart(salinityChart, salinityChartInstance, 'salinity', '盐度', 'rgba(75, 192, 192, 1)');
      waterLevelChartInstance = initWaterQualityChart(waterLevelChart, waterLevelChartInstance, 'waterLevel', '水位', 'rgba(255, 159, 64, 1)');
      oxygenChartInstance = initWaterQualityChart(oxygenChart, oxygenChartInstance, 'oxygen', '溶解氧', 'rgba(153, 102, 255, 1)');
    };

    // 更新所有水质参数图表
    const updateWaterQualityCharts = (dayNumber) => {
      const data = getWaterQualityData(dayNumber);
      
      if (tempChartInstance) {
        tempChartInstance.data.labels = data.labels;
        tempChartInstance.data.datasets[0].data = data.temperature;
        tempChartInstance.update('active');
      }
      
      if (phChartInstance) {
        phChartInstance.data.labels = data.labels;
        phChartInstance.data.datasets[0].data = data.ph;
        phChartInstance.update('active');
      }
      
      if (salinityChartInstance) {
        salinityChartInstance.data.labels = data.labels;
        salinityChartInstance.data.datasets[0].data = data.salinity;
        salinityChartInstance.update('active');
      }
      
      if (waterLevelChartInstance) {
        waterLevelChartInstance.data.labels = data.labels;
        waterLevelChartInstance.data.datasets[0].data = data.waterLevel;
        waterLevelChartInstance.update('active');
      }
      
      if (oxygenChartInstance) {
        oxygenChartInstance.data.labels = data.labels;
        oxygenChartInstance.data.datasets[0].data = data.oxygen;
        oxygenChartInstance.update('active');
      }
    };

 

    // 更新水质参数图表
    const updateWaterQualityChart = (dayNumber) => {
      if (waterQualityChartInstance) {
        const data = getWaterQualityData(dayNumber);
        
        // 更新数据集
        waterQualityChartInstance.data.datasets[0].data = Object.values(data);
        
        // 更新图表
        waterQualityChartInstance.update('active');
      }
    };

    
    // 响应窗口大小变化，调整圆点大小
    const handleResize = () => {
      generateTankGrid();
    };
    
    // 生命周期钩子
    onMounted(() => {
      // 初始化养殖池数据
      generateTankGrid();

      resetToProgress()
      
      // 初始化水质参数图表
      initWaterQualityCharts();
      updateWaterQualityCharts(sliderValue.value);

      // 添加窗口大小变化监听
      window.addEventListener('resize', handleResize);
      
      // 组件卸载时移除监听
      return () => {
        window.removeEventListener('resize', handleResize);
        if (restoreFeedbackTimer.value) {
          clearTimeout(restoreFeedbackTimer.value);
          // 销毁所有图表实例
        [tempChartInstance, phChartInstance, salinityChartInstance, waterLevelChartInstance, oxygenChartInstance]
          .forEach(instance => instance && instance.destroy());
        }
      };
    });
    
    return {
      buildingOpen,
      activeFloor,
      activeDay,
      sliderValue,
      fillPercent,
      progressPercent,
      tanks,
      days,
      feedInfo,
      waterQualityChart,
      selectedTankId,
      showRestoreFeedback,
      tempChart,
      phChart,
      salinityChart,
      waterLevelChart,
      oxygenChart,
      toggleBuildingMenu,
      setActiveFloor,
      setActiveDay,
      onSliderInput,
      resetToProgress,
      refreshTankGrid,
      selectTank,
      getStatusColor
    };
  }
};
</script>

<style scoped>
/* 基础样式 */
html, body {
  padding: 0;
  margin: 0;
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.scale-hover {
  transition: transform 0.2s ease;
}

.scale-hover:hover {
  transform: scale(1.05);
}

/* 监控区域动画 */
.monitor-pulse {
  position: relative;
  overflow: hidden;
}

.monitor-pulse::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 50%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  animation: shine 2s infinite;
}

@keyframes shine {
  100% {
    left: 100%;
  }
}

/* 导入自定义工具类的样式实现 */
.tank-dot {
  border-radius: 50%;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  position: relative;
}

.tank-dot-normal {
  background-color: rgba(82, 196, 26, 0.2);
  border: 2px solid #52C41A;
}

.tank-dot-warning {
  background-color: rgba(250, 173, 20, 0.2);
  border: 2px solid #FAAD14;
}

.tank-dot-alert {
  background-color: rgba(255, 77, 79, 0.2);
  border: 2px solid #FF4D4F;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.nav-item-active {
  background-color: rgba(22, 93, 255, 0.1);
  color: #165DFF;
  border-left: 4px solid #165DFF;
}

.day-tab-active {
  background-color: #165DFF;
  color: white;
}

/* 滑动条样式 */
.slider {
  -webkit-appearance: none;
  appearance: none;
  height: 12px;
  background: linear-gradient(90deg, 
    #52C41A 0%, 
    #52C41A var(--progress, 0%), 
    #165DFF var(--progress, 0%), 
    #165DFF var(--fill, 0%), 
    #E5E7EB var(--fill, 0%), 
    #E5E7EB 100%
  );
  border-radius: 9999px;
  outline: none;
  box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 28px;
  height: 28px;
  border-radius: 9999px;
  background: #165DFF;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  cursor: pointer;
  transition: transform 0.1s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.slider::-moz-range-thumb {
  width: 28px;
  height: 28px;
  border-radius: 9999px;
  background: #165DFF;
  border: 3px solid white;
  box-shadow: 0 2px 8px rgba(0,0,0,0.2);
  cursor: pointer;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 新增选中状态样式 */
.tank-selected {
  box-shadow: 0 0 0 5px #165DFF, 0 0 12px 6px rgba(22, 93, 255, 0.4);
  transform: scale(1.1);
  z-index: 10;
  transition: all 0.3s ease;
}

/* 确保选中状态在不同状态池子上都可见 */
.tank-dot-normal.tank-selected {
  background-color: rgba(82, 196, 26, 0.4);
}

.tank-dot-warning.tank-selected {
  background-color: rgba(250, 173, 20, 0.4);
}

.tank-dot-alert.tank-selected {
  background-color: rgba(255, 77, 79, 0.4);
}

/* 恢复反馈动画 */
.restore-feedback {
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  50% { opacity: 1; }
}

.restore-button-animate {
  animation: buttonPulse 0.5s ease-in-out;
}

@keyframes buttonPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}
</style>