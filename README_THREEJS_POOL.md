# Three.js 池塘查看器组件

## 概述

`ThreeJSPoolViewer` 是一个基于 Three.js 的 3D 池塘可视化组件，用于替代原来的静态池塘图片。该组件提供了交互式的 3D 池塘模型，包含动画效果和实时信息显示。

## 功能特性

### 🎯 核心功能
- **3D 池塘模型**: 程序生成的真实感池塘模型
- **动态动画**: 相机自动旋转、气泡上升效果
- **交互控制**: 播放/暂停动画、重置视角
- **实时信息**: 显示水温、pH值、溶解氧等关键参数

### 🎨 视觉效果
- **真实材质**: 使用 PBR 材质模拟水面、混凝土边缘
- **光照系统**: 环境光 + 定向光 + 点光源组合
- **气泡动画**: 从增氧设备产生的动态气泡效果
- **设备模型**: 增氧机、投料机等养殖设备

### 🔧 技术特点
- **RFA 文件支持**: 尝试加载 Revit 族文件，失败时回退到程序生成模型
- **响应式设计**: 自适应容器大小变化
- **性能优化**: 高效的动画循环和资源管理
- **TypeScript**: 完整的类型支持

## 安装依赖

```bash
pnpm add three @types/three three-stdlib
```

## 使用方法

### 基本用法

```vue
<template>
  <div class="container">
    <ThreeJSPoolViewer 
      class="pool-viewer"
      @click="handlePoolClick"
    />
  </div>
</template>

<script setup lang="ts">
import ThreeJSPoolViewer from '@/components/ThreeJSPoolViewer.vue'

const handlePoolClick = () => {
  console.log('池塘被点击了')
}
</script>

<style scoped>
.pool-viewer {
  width: 100%;
  height: 500px;
}
</style>
```

### 事件

| 事件名 | 说明 | 参数 |
|--------|------|------|
| `click` | 点击池塘时触发 | 无 |

### 控制功能

组件内置了控制面板，包含以下功能：

1. **播放/暂停**: 控制相机旋转和气泡动画
2. **重置视角**: 将相机重置到初始位置

### 信息面板

左上角显示池塘的关键参数：
- 水温 (°C)
- pH值
- 溶解氧 (mg/L)

## 文件结构

```
src/
├── components/
│   └── ThreeJSPoolViewer.vue    # 主组件文件
├── assets/
│   ├── pool.rfa                 # Revit 族文件（尝试加载）
│   └── images/
│       └── pool.png            # 原始池塘图片（已替换）
└── pages/
    └── aquaculture-dashboard2.vue  # 使用组件的页面
```

## 技术实现

### 3D 模型构建

```typescript
// 池塘底部
const poolGeometry = new THREE.CylinderGeometry(10, 10, 1.5, 64)
const poolMaterial = new THREE.MeshPhongMaterial({
  color: 0x2c5f2d,
  transparent: true,
  opacity: 0.9,
})

// 水面
const waterGeometry = new THREE.CylinderGeometry(9.8, 9.8, 0.1, 64)
const waterMaterial = new THREE.MeshPhongMaterial({
  color: 0x1e6091,
  transparent: true,
  opacity: 0.7,
  shininess: 100,
  reflectivity: 0.8,
})
```

### 动画系统

```typescript
const animate = () => {
  // 相机旋转
  const time = Date.now() * 0.0005
  camera.position.x = Math.cos(time) * 25
  camera.position.z = Math.sin(time) * 25
  
  // 气泡上升
  bubbles.forEach(bubble => {
    bubble.position.y += bubble.userData.speed
    if (bubble.position.y > 1.5) {
      bubble.position.y = bubble.userData.originalY
    }
  })
}
```

## 自定义配置

### 修改池塘参数

可以通过修改 `createPoolGeometry` 函数来调整池塘的外观：

```typescript
// 调整池塘大小
const poolRadius = 10  // 池塘半径
const poolDepth = 1.5  // 池塘深度

// 调整气泡数量
const bubbleCount = 30  // 气泡数量

// 调整动画速度
const rotationSpeed = 0.0005  // 相机旋转速度
const bubbleSpeed = 0.01      // 气泡上升速度
```

### 添加新设备

```typescript
// 添加新的养殖设备
const newEquipment = new THREE.BoxGeometry(1, 1, 1)
const equipmentMaterial = new THREE.MeshPhongMaterial({ color: 0x666666 })
const equipment = new THREE.Mesh(newEquipment, equipmentMaterial)
equipment.position.set(x, y, z)
group.add(equipment)
```

## 性能优化建议

1. **LOD (Level of Detail)**: 根据相机距离调整模型细节
2. **实例化**: 对于大量相似对象（如气泡）使用 InstancedMesh
3. **纹理优化**: 使用压缩纹理格式
4. **几何体合并**: 合并静态几何体减少绘制调用

## 故障排除

### 常见问题

1. **模型不显示**: 检查容器尺寸是否正确设置
2. **动画卡顿**: 检查浏览器性能，考虑降低模型复杂度
3. **RFA 文件加载失败**: 这是预期行为，组件会自动回退到程序生成模型

### 调试技巧

```typescript
// 启用 Three.js 调试信息
console.log('Scene objects:', scene.children.length)
console.log('Renderer info:', renderer.info)

// 添加坐标轴辅助器
const axesHelper = new THREE.AxesHelper(5)
scene.add(axesHelper)
```

## 未来改进

- [ ] 支持更多 3D 文件格式 (GLTF, OBJ, FBX)
- [ ] 添加水波纹效果
- [ ] 实现鱼类游动动画
- [ ] 添加天气效果（雨、雾等）
- [ ] 支持 VR/AR 查看模式
- [ ] 添加声音效果

## 许可证

本组件遵循项目的整体许可证协议。
