<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">易耗品管理</h1>
      <p class="page-subtitle">易耗品库存与使用管理</p>
    </div>
    
    <div class="page-content">
      <div class="content-grid">
        <!-- 易耗品统计卡片 -->
        <div class="stat-card">
          <div class="stat-icon">
            <div class="i-mdi-package-variant text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">245</div>
            <div class="stat-label">易耗品种类</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon sufficient">
            <div class="i-mdi-check-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">198</div>
            <div class="stat-label">库存充足</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon warning">
            <div class="i-mdi-alert-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">32</div>
            <div class="stat-label">库存不足</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon shortage">
            <div class="i-mdi-close-circle text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">15</div>
            <div class="stat-label">缺货</div>
          </div>
        </div>
      </div>
      
      <!-- 易耗品列表 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>易耗品库存列表</h3>
          <div class="panel-actions">
            <button class="btn-primary">入库</button>
            <button class="btn-secondary">出库</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>物品编号</th>
                  <th>物品名称</th>
                  <th>规格型号</th>
                  <th>单位</th>
                  <th>当前库存</th>
                  <th>安全库存</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in consumableList" :key="item.id">
                  <td>{{ item.code }}</td>
                  <td>{{ item.name }}</td>
                  <td>{{ item.specification }}</td>
                  <td>{{ item.unit }}</td>
                  <td>{{ item.currentStock }}</td>
                  <td>{{ item.safetyStock }}</td>
                  <td>
                    <span class="status-badge" :class="item.status">
                      {{ item.statusText }}
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">入库</button>
                      <button class="btn-small">出库</button>
                      <button class="btn-small btn-info">详情</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface Consumable {
  id: number
  code: string
  name: string
  specification: string
  unit: string
  currentStock: number
  safetyStock: number
  status: string
  statusText: string
}

const consumableList = ref<Consumable[]>([
  {
    id: 1,
    code: 'CON001',
    name: '鱼饲料',
    specification: '高蛋白配方',
    unit: '袋',
    currentStock: 150,
    safetyStock: 50,
    status: 'sufficient',
    statusText: '库存充足'
  },
  {
    id: 2,
    code: 'CON002',
    name: '水质调节剂',
    specification: '500ml/瓶',
    unit: '瓶',
    currentStock: 25,
    safetyStock: 30,
    status: 'warning',
    statusText: '库存不足'
  },
  {
    id: 3,
    code: 'CON003',
    name: '增氧片',
    specification: '1kg/包',
    unit: '包',
    currentStock: 0,
    safetyStock: 20,
    status: 'shortage',
    statusText: '缺货'
  },
  {
    id: 4,
    code: 'CON004',
    name: '消毒液',
    specification: '2L/桶',
    unit: '桶',
    currentStock: 45,
    safetyStock: 15,
    status: 'sufficient',
    statusText: '库存充足'
  },
  {
    id: 5,
    code: 'CON005',
    name: '过滤棉',
    specification: '标准规格',
    unit: '片',
    currentStock: 80,
    safetyStock: 100,
    status: 'warning',
    statusText: '库存不足'
  }
])
</script>

<style scoped>
.page-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(102, 204, 255, 0.1);
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 204, 255, 0.2);
  color: #66ccff;
}

.stat-icon.sufficient {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-icon.warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.stat-icon.shortage {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #66ccff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(102, 204, 255, 0.7);
}

.data-panel {
  background: rgba(102, 204, 255, 0.05);
  border: 1px solid rgba(102, 204, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #66ccff;
}

.panel-actions {
  display: flex;
  gap: 12px;
}

.btn-primary {
  background: linear-gradient(135deg, #66ccff, #33aaff);
  color: #001122;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border: 1px solid rgba(102, 204, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover,
.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 204, 255, 0.3);
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(102, 204, 255, 0.1);
}

.data-table th {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  font-weight: 600;
}

.data-table td {
  color: rgba(102, 204, 255, 0.9);
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.sufficient {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.warning {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.status-badge.shortage {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 12px;
  border: 1px solid rgba(102, 204, 255, 0.3);
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: rgba(102, 204, 255, 0.2);
}

.btn-small.btn-info {
  border-color: rgba(168, 85, 247, 0.3);
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.btn-small.btn-info:hover {
  background: rgba(168, 85, 247, 0.2);
}
</style>
