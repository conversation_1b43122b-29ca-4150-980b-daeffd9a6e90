<template>
  <div class="flex h-screen">
  
    
    <!-- 中间：3x3监控画面区域 -->
    <main class="flex-1 flex flex-col bg-dark p-4 overflow-hidden">
      <div class="mb-4 flex justify-between items-center">
        <h2 class="text-lg font-semibold text-white">监控画面 (3×3)</h2>
        <div class="flex items-center space-x-3">
          <div class="text-sm text-white">
            <span>{{ currentTime }}</span>
          </div>
          <button class="px-3 py-1 bg-primary/20 hover:bg-primary/30 text-primary rounded text-sm transition-colors">
            <i class="fa fa-refresh mr-1"></i> 刷新
          </button>
        </div>
      </div>
      
      <!-- 3x3监控网格 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 flex-1 overflow-y-auto">
        <!-- 养殖区域监控 -->
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'c001' }"
          @click="openFullscreen('c001', '养殖区C001', '@/assets/images/monitoring/jk1.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">养殖区C001</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video relative">
            <img src="@/assets/images/monitoring/jk1.png" alt="养殖区C001监控画面" class="w-full h-full object-cover">
            <div class="absolute top-2 right-2 bg-danger text-white text-xs px-2 py-1 rounded-full alert-pulse">
              <i class="fa fa-exclamation-circle mr-1"></i> 告警
            </div>
          </div>
        </div>
        
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'c002' }"
          @click="openFullscreen('c002', '养殖区C002', '@/assets/images/monitoring/jk2.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">养殖区C002</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video">
            <img src="@/assets/images/monitoring/jk2.png" alt="养殖区C002监控画面" class="w-full h-full object-cover">
          </div>
        </div>
        
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'c003' }"
          @click="openFullscreen('c003', '养殖区C003', '@/assets/images/monitoring/jk3.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">养殖区C003</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video">
            <img src="@/assets/images/monitoring/jk3.png" alt="养殖区C003监控画面" class="w-full h-full object-cover">
          </div>
        </div>
        
        <!-- 园区门口监控 -->
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'gate1' }"
          @click="openFullscreen('gate1', '东门入口', '@/assets/images/monitoring/jk4.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">东门入口</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video">
            <img src="@/assets/images/monitoring/jk4.png" alt="东门入口监控画面" class="w-full h-full object-cover">
          </div>
        </div>
        
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'gate2' }"
          @click="openFullscreen('gate2', '西门入口', '@/assets/images/monitoring/jk5.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">西门入口</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video">
            <img src="@/assets/images/monitoring/jk5.png" alt="西门入口监控画面" class="w-full h-full object-cover">
          </div>
        </div>
        
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'parking' }"
          @click="openFullscreen('parking', '停车场', '@/assets/images/monitoring/jk6.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">停车场</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video">
            <img src="@/assets/images/monitoring/jk6.png" alt="停车场监控画面" class="w-full h-full object-cover">
          </div>
        </div>
        
        <!-- 办公区域监控 -->
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'office1' }"
          @click="openFullscreen('office1', '办公区大厅', '@/assets/images/monitoring/jk7.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">办公区大厅</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video">
            <img src="@/assets/images/monitoring/jk7.png" alt="办公区大厅监控画面" class="w-full h-full object-cover">
          </div>
        </div>
        
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'b202' }"
          @click="openFullscreen('b202', '二楼走廊', '@/assets/images/monitoring/jk8.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">二楼走廊</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video relative">
            <img src="@/assets/images/monitoring/jk8.png" alt="二楼走廊监控画面" class="w-full h-full object-cover">
            <div class="absolute top-2 right-2 bg-warning text-white text-2xl px-2 py-1 rounded-full alert-pulse">
              <i class="fa fa-exclamation-triangle mr-1"></i> 水雾
            </div>
          </div>
        </div>
        
        <div 
          class="monitor-item bg-dark-light rounded-lg overflow-hidden border border-dark-lighter" 
          :class="{ 'active': activeMonitor === 'meeting' }"
          @click="openFullscreen('meeting', '会议室门口', '@/assets/images/monitoring/jk9.png')"
        >
          <div class="p-2 bg-dark-lighter flex justify-between items-center">
            <span class="text-sm font-medium">会议室门口</span>
            <span class="text-xs text-gray-400">实时</span>
          </div>
          <div class="aspect-video">
            <img src="@/assets/images/monitoring/jk9.png" alt="会议室门口监控画面" class="w-full h-full object-cover">
          </div>
        </div>
      </div>
    </main>
    
    <!-- 右侧：告警信息和设备数据 -->
    <aside class="w-[22%] flex flex-col bg-dark-light border-l border-dark-lighter overflow-hidden">
      <!-- 抽烟监控告警 -->
      <!-- 仅展示修改的告警信息部分，完整代码需结合原有内容 -->
        <!-- 监控告警信息 -->
<div class="p-4 border-b border-dark-lighter">
  <h2 class="text-lg font-semibold text-white mb-4 flex items-center">
    <i class="fa fa-bell text-warning mr-2"></i>
    监控告警信息
  </h2>
  
  <!-- 滚动容器 -->
  <div class="relative overflow-hidden h-[35vh]">
    <!-- 滚动内容 -->
    <div class="space-y-3 pr-2 auto-scroll-container">
      <!-- 抽烟检测告警 -->
      <div class="bg-[#1E1A25] rounded-lg p-3 border-l-4 border-red-500 shadow-lg">
        <div class="flex justify-between items-start">
          <h3 class="font-medium text-red-400 text-base">抽烟检测告警</h3>
          <span class="text-xs text-white bg-[#2C2C3A] px-2 py-0.5 rounded">10分钟前</span>
        </div>
        <p class="text-sm mt-2 text-gray-300">C001区域检测到人员抽烟行为，请及时处理</p>
        <div class="mt-3 flex justify-end">
          <button class="text-xs text-white bg-red-600 hover:bg-red-700 px-3 py-1 rounded transition-colors">
            已处理
          </button>
        </div>
      </div>
      
      <!-- 摄像头异常告警 -->
      <div class="bg-[#1E1A25] rounded-lg p-3 border-l-4 border-orange-500 shadow-lg">
        <div class="flex justify-between items-start">
          <h3 class="font-medium text-orange-400 text-base">摄像头异常</h3>
          <span class="text-xs text-white bg-[#2C2C3A] px-2 py-0.5 rounded">25分钟前</span>
        </div>
        <p class="text-sm mt-2 text-gray-300">B202区域摄像头出现水雾，影响监控效果</p>
        <div class="mt-3 flex justify-end">
          <button class="text-xs text-white bg-orange-600 hover:bg-orange-700 px-3 py-1 rounded transition-colors">
            已安排清洁
          </button>
        </div>
      </div>
      
      <!-- 区域闯入告警 -->
      <div class="bg-[#1E1A25] rounded-lg p-3 border-l-4 border-blue-500 shadow-lg">
        <div class="flex justify-between items-start">
          <h3 class="font-medium text-blue-400 text-base">区域闯入</h3>
          <span class="text-xs text-white bg-[#2C2C3A] px-2 py-0.5 rounded">1小时前</span>
        </div>
        <p class="text-sm mt-2 text-gray-300">非工作时间有人进入C003养殖区</p>
        <div class="mt-3 flex justify-end">
          <button class="text-xs text-white bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded transition-colors">
            已核实
          </button>
        </div>
      </div>
      
      <!-- 设备离线告警 -->
      <div class="bg-[#1E1A25] rounded-lg p-3 border-l-4 border-gray-500 shadow-lg">
        <div class="flex justify-between items-start">
          <h3 class="font-medium text-gray-400 text-base">设备离线</h3>
          <span class="text-xs text-white bg-[#2C2C3A] px-2 py-0.5 rounded">3小时前</span>
        </div>
        <p class="text-sm mt-2 text-gray-300">东门入口摄像头短暂离线，已自动恢复</p>
        <div class="mt-3 flex justify-end">
          <span class="text-xs text-white bg-green-600 hover:bg-green-700 px-3 py-1 rounded transition-colors">已处理</span>
        </div>
      </div>

      <!-- 重复内容用于无缝滚动 -->
      <!-- 抽烟检测告警 -->
      <div class="bg-[#1E1A25] rounded-lg p-3 border-l-4 border-red-500 shadow-lg">
        <div class="flex justify-between items-start">
          <h3 class="font-medium text-red-400 text-base">抽烟检测告警</h3>
          <span class="text-xs text-white bg-[#2C2C3A] px-2 py-0.5 rounded">10分钟前</span>
        </div>
        <p class="text-sm mt-2 text-gray-300">C001区域检测到人员抽烟行为，请及时处理</p>
        <div class="mt-3 flex justify-end">
          <button class="text-xs text-white bg-red-600 hover:bg-red-700 px-3 py-1 rounded transition-colors">
            已处理
          </button>
        </div>
      </div>
      
      <!-- 摄像头异常告警 -->
      <div class="bg-[#1E1A25] rounded-lg p-3 border-l-4 border-orange-500 shadow-lg">
        <div class="flex justify-between items-start">
          <h3 class="font-medium text-orange-400 text-base">摄像头异常</h3>
          <span class="text-xs text-white bg-[#2C2C3A] px-2 py-0.5 rounded">25分钟前</span>
        </div>
        <p class="text-sm mt-2 text-gray-300">B202区域摄像头出现水雾，影响监控效果</p>
        <div class="mt-3 flex justify-end">
          <button class="text-xs text-white bg-orange-600 hover:bg-orange-700 px-3 py-1 rounded transition-colors">
            已安排清洁
          </button>
        </div>
      </div>
    </div>
  </div>
</div>

      
      <!-- 设备数据信息 -->
      <div class="flex-1 p-4 overflow-y-auto">
        <h2 class="text-lg font-semibold text-white mb-4 flex items-center">
          <i class="fa fa-cogs text-primary mr-2"></i>
          设备状态统计
        </h2>
        
        <!-- 设备状态概览 -->
        <div class="grid grid-cols-2 gap-3 mb-6">
          <div class="bg-dark-lighter rounded-lg p-3 text-center">
            <div class="text-gray-400 text-sm">设备总数</div>
            <div class="text-2xl font-bold text-white mt-1">{{ deviceStats.total }}</div>
          </div>
          
          <div class="bg-dark-lighter rounded-lg p-3 text-center">
            <div class="text-gray-400 text-sm">正常运行</div>
            <div class="text-2xl font-bold text-green mt-1">{{ deviceStats.normal }}</div>
          </div>
          
          <div class="bg-dark-lighter rounded-lg p-3 text-center">
            <div class="text-gray-400 text-sm">异常设备</div>
            <div class="text-2xl font-bold text-orange mt-1">{{ deviceStats.abnormal }}</div>
          </div>
          
          <div class="bg-dark-lighter rounded-lg p-3 text-center">
            <div class="text-gray-400 text-sm">离线设备</div>
            <div class="text-2xl font-bold text-gray mt-1">{{ deviceStats.offline }}</div>
          </div>
        </div>
        
        <!-- 设备类型分布 -->
        <div class="mb-6">
          <h3 class="text-sm font-medium text-gray-300 mb-3">设备类型分布</h3>
          <div class="h-48">
            <canvas id="deviceTypeChart"></canvas>
          </div>
        </div>
        
        <!-- 异常设备列表 -->
        <div>
          <h3 class="text-sm font-medium text-gray-300 mb-3">异常设备详情</h3>
          <div class="space-y-2">
            <div v-for="(device, index) in abnormalDevices" :key="index" class="bg-dark-lighter text-white rounded-lg p-2 flex justify-between items-center">
              <div class="flex items-center">
                <i :class="['mr-2', device.iconClass]"></i>
                <span class="text-sm">{{ device.name }}</span>
              </div>
              <span :class="['text-xs px-2 py-0.5 rounded', device.statusClass]">{{ device.status }}</span>
            </div>
          </div>
        </div>
      </div>
    </aside>
    
    <!-- 全屏监控弹窗 -->
    <div class="fullscreen-monitor" :class="{ 'active': isFullscreen }">
      <button id="closeFullscreen" class="absolute top-4 right-4 w-10 h-10 bg-dark/50 hover:bg-dark rounded-full flex items-center justify-center text-white z-10"
        @click="closeFullscreen">
        <i class="fa fa-times"></i>
      </button>
      <h3 id="fullscreenTitle" class="text-xl font-bold mb-4 text-white">{{ fullscreenTitle }}</h3>
      <div class="max-w-4xl w-full mx-auto relative">
        <img id="fullscreenImage" :src="fullscreenImage" alt="监控放大画面" class="w-full">
        <div class="absolute bottom-4 left-0 right-0 flex justify-center space-x-4">
          <button class="bg-dark/70 hover:bg-dark text-white px-3 py-1 rounded text-sm">
            <i class="fa fa-play mr-1"></i> 录像
          </button>
          <button class="bg-dark/70 hover:bg-dark text-white px-3 py-1 rounded text-sm">
            <i class="fa fa-search-plus mr-1"></i> 放大
          </button>
          <button class="bg-dark/70 hover:bg-dark text-white px-3 py-1 rounded text-sm">
            <i class="fa fa-refresh mr-1"></i> 刷新
          </button>
          <button class="bg-dark/70 hover:bg-dark text-white px-3 py-1 rounded text-sm">
            <i class="fa fa-history mr-1"></i> 回放
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';
import Chart from 'chart.js/auto';

export default {
  name: 'AquacultureMonitor',
  setup() {
    // 响应式数据
    const activeNav = ref('breeding');
    const currentTime = ref('');
    const activeMonitor = ref('');
    const isFullscreen = ref(false);
    const fullscreenTitle = ref('');
    const fullscreenImage = ref('');
    
    // 设备统计数据
    const deviceStats = ref({
      total: 86,
      normal: 79,
      abnormal: 5,
      offline: 2
    });
    
    // 异常设备列表
    const abnormalDevices = ref([
      { 
        name: 'B202摄像头', 
        iconClass: 'fa fa-video-camera text-danger', 
        status: '水雾',
        statusClass: 'bg-warning/20 text-warning'
      },
      { 
        name: 'C001摄像头', 
        iconClass: 'fa fa-video-camera text-danger', 
        status: '需关注',
        statusClass: 'bg-danger/20 text-danger'
      },
      { 
        name: 'C005传感器', 
        iconClass: 'fa fa-tint text-danger', 
        status: '数据波动',
        statusClass: 'bg-warning/20 text-warning'
      },
      { 
        name: '东门电力柜', 
        iconClass: 'fa fa-bolt text-danger', 
        status: '温度偏高',
        statusClass: 'bg-warning/20 text-warning'
      },
      { 
        name: 'C008控制器', 
        iconClass: 'fa fa-wifi text-danger', 
        status: '离线',
        statusClass: 'bg-danger/20 text-danger'
      }
    ]);
    
    // 更新当前时间
    function updateCurrentTime() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      
      currentTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    
    // 打开全屏监控
    function openFullscreen(id, title, image) {
      activeMonitor.value = id;
      fullscreenTitle.value = title;
      fullscreenImage.value = image;
      isFullscreen.value = true;
      document.body.style.overflow = 'hidden';
    }
    
    // 关闭全屏监控
    function closeFullscreen() {
      isFullscreen.value = false;
      document.body.style.overflow = 'auto';
    }
    
    // 处理告警
    function handleAlert(type) {
      // 这里可以添加处理告警的逻辑
      console.log(`处理告警: ${type}`);
      // 实际应用中可能会调用API更新告警状态
    }
    
    // 初始化设备类型图表
    function initDeviceChart() {
      const ctx = document.getElementById('deviceTypeChart').getContext('2d');
      new Chart(ctx, {
        type: 'doughnut',
        data: {
          labels: ['监控摄像头', '水质传感器', '控制设备', '电力设备', '其他设备'],
          datasets: [{
            data: [32, 24, 16, 8, 6],
            backgroundColor: [
              '#165DFF',
              '#00B42A',
              '#FF7D00',
              '#722ED1',
              '#86909C'
            ],
            borderWidth: 0,
            hoverOffset: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          devicePixelRatio: 2,
          cutout: '70%',
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                color: '#ffffff',
                padding: 10,
                boxWidth: 8,
                usePointStyle: true,
                pointStyle: 'circle',
                font: {
                  size: 12
                }
              }
            }
          }
        }
      });
    }
    
    // 页面加载时初始化
    onMounted(() => {
      updateCurrentTime();
      setInterval(updateCurrentTime, 1000);
      initDeviceChart();
    });
    
    return {
      activeNav,
      currentTime,
      activeMonitor,
      isFullscreen,
      fullscreenTitle,
      fullscreenImage,
      deviceStats,
      abnormalDevices,
      openFullscreen,
      closeFullscreen,
      handleAlert
    };
  }
};
</script>

<style scoped>
/* 引入外部资源的样式会保留在全局，这里只放置组件特定样式 */
/* 基础样式 */
::v-deep body {
  background-color: #0F172A;
  color: #fff;
  height: 100vh;
  overflow: hidden;
}

/* 监控画面样式 */
.monitor-item {
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.monitor-item:hover {
  transform: scale(1.02);
}

.monitor-item.active {
  border-color: #165DFF;
  box-shadow: 0 0 10px rgba(22, 93, 255, 0.5);
}

.monitor-placeholder {
  background: linear-gradient(45deg, #1E293B 25%, #334155 25%, #334155 50%, #1E293B 50%, #1E293B 75%, #334155 75%, #334155 100%);
  background-size: 20px 20px;
}

/* 全屏监控样式 */
.fullscreen-monitor {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9);
  z-index: 100;
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.fullscreen-monitor.active {
  display: flex;
}

/* 告警动画 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.alert-pulse {
  animation: pulse 2s infinite;
}

/* 滚动条美化 */
::v-deep ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::v-deep ::-webkit-scrollbar-track {
  background: #1E293B;
  border-radius: 3px;
}

::v-deep ::-webkit-scrollbar-thumb {
  background: #0ac6df;
  border-radius: 3px;
}

::v-deep ::-webkit-scrollbar-thumb:hover {
  background: #475569;
}

/* 自动滚动容器 */
.auto-scroll-container {
  animation: autoScroll 30s linear infinite;
}

/* 鼠标悬停时暂停滚动 */
.auto-scroll-container:hover {
  animation-play-state: paused;
}

/* 滚动动画 */
@keyframes autoScroll {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%);
  }
}

/* 确保滚动容器正确设置 */
.relative.overflow-hidden {
  mask-image: linear-gradient(
    to bottom,
    transparent 0%,
    black 10%,
    black 90%,
    transparent 100%
  );
}
</style>
