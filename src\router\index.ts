import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
import AquacultureHomepage from '../pages/aquaculture-homepage.vue'
import Dashboard2 from '../pages/aquaculture-dashboard2.vue'
import SchedulingCenter from '../pages/scheduling-center.vue'
import AquacultureManagement from '../pages/aquacultureProcess/aquaculture-management.vue'
import VideoSecurity from '../pages/video-security.vue'
import GardenVideo from '../pages/garden-video.vue'
import GardenSecurity from '../pages/garden-security.vue'
import Traceability from '../pages/traceability.vue'
import OASystem from '../pages/oa-system.vue'
import PartyBuilding from '../pages/party-building.vue'
import HRSystem from '../pages/hr-system.vue'
import WarehouseManagement from '../pages/warehouse-management.vue'
import MarketingManagement from '../pages/marketing-management.vue'
import EquipmentManagement from '../pages/equipment-management.vue'
import AIModel from '../pages/ai-model.vue'
import AIAssistant from '../pages/ai-assistant.vue'

// 创建资产管理相关页面组件（如果不存在的话，先创建占位组件）
const DeviceManagement = () => import('../pages/asset/device-management.vue')
const FixedAssetManagement = () => import('../pages/asset/fixed-asset-management.vue')
const ConsumableManagement = () => import('../pages/asset/consumable-management.vue')

// 创建园区管理相关页面组件
const SecurityManagement = () => import('../pages/park/security-management.vue')
const AccessControlManagement = () => import('../pages/park/access-control-management.vue')
const BroadcastManagement = () => import('../pages/park/broadcast-management.vue')

const routes = [
  {
    path: '/',
    redirect: '/homepage'
  },
  {
    path: '/homepage',
    name: 'Homepage',
    component: AquacultureHomepage,
    meta: { title: '总览' }
  },
  {
    path: '/dashboard2/:floorName?',
    name: 'Dashboard2',
    component: Dashboard2,
    meta: { title: '池子详情' }
  },
  {
    path: '/aquaculture-management',
    name: 'AquacultureManagement',
    component: AquacultureManagement,
    meta: { title: '养殖过程管理' }
  },
  {
    path: '/ai-model',
    name: 'AIModel',
    component: AIModel,
    meta: { title: '智能AI大模型' }
  },
  // 资产管理路由组
  {
    path: '/asset',
    name: 'AssetManagement',
    redirect: '/asset/device',
    meta: { title: '资产管理' },
    children: [
      {
        path: 'device',
        name: 'DeviceManagement',
        component: DeviceManagement,
        meta: { title: '设备管理' }
      },
      {
        path: 'fixed-asset',
        name: 'FixedAssetManagement',
        component: FixedAssetManagement,
        meta: { title: '固定资产管理' }
      },
      {
        path: 'consumable',
        name: 'ConsumableManagement',
        component: ConsumableManagement,
        meta: { title: '易耗品管理' }
      }
    ]
  },
  // 园区管理路由组
  {
    path: '/park',
    name: 'ParkManagement',
    redirect: '/park/security',
    meta: { title: '园区管理' },
    children: [
      {
        path: 'security',
        name: 'SecurityManagement',
        component: SecurityManagement,
        meta: { title: '安防' }
      },
      {
        path: 'access-control',
        name: 'AccessControlManagement',
        component: AccessControlManagement,
        meta: { title: '门禁' }
      },
      {
        path: 'broadcast',
        name: 'BroadcastManagement',
        component: BroadcastManagement,
        meta: { title: '广播' }
      }
    ]
  },
  {
    path: '/warehouse-management',
    name: 'WarehouseManagement',
    component: WarehouseManagement,
    meta: { title: '进销存' }
  },
  {
    path: '/oa-system',
    name: 'OASystem',
    component: OASystem,
    meta: { title: '在线办公' }
  },
  {
    path: '/party-building',
    name: 'PartyBuilding',
    component: PartyBuilding,
    meta: { title: '党建平台' }
  },
  // 其他页面路由（保留但不在主菜单中显示）
  {
    path: '/scheduling-center',
    name: 'SchedulingCenter',
    component: SchedulingCenter,
    meta: { title: '调度中心' }
  },
  {
    path: '/video-security',
    name: 'VideoSecurity',
    component: VideoSecurity,
    meta: { title: '视频安防' }
  },
  {
    path: '/garden-video',
    name: 'GardenVideo',
    component: GardenVideo,
    meta: { title: '园区视频' }
  },
  {
    path: '/garden-security',
    name: 'GardenSecurity',
    component: GardenSecurity,
    meta: { title: '园区安防' }
  },
  {
    path: '/traceability',
    name: 'Traceability',
    component: Traceability,
    meta: { title: '溯源系统' }
  },
  {
    path: '/hr-system',
    name: 'HRSystem',
    component: HRSystem,
    meta: { title: '人力资源' }
  },
  {
    path: '/marketing-management',
    name: 'MarketingManagement',
    component: MarketingManagement,
    meta: { title: '营销管理' }
  },
  {
    path: '/equipment-management',
    name: 'EquipmentManagement',
    component: EquipmentManagement,
    meta: { title: '设备管理' }
  },
  {
    path: '/ai-assistant',
    name: 'AIAssistant',
    component: AIAssistant,
    meta: { title: 'AI助手' }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
