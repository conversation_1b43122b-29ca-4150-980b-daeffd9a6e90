<template>
  <div class="aquaculture-dashboard">
    <!-- 顶部标题栏 -->
    <div class="header-section">
      <div class="header-left"></div>
      <div class="header-center">
        <h1 class="main-title">{{ dynamicTitle }}</h1>
      </div>
      <div class="header-right">
        <!-- 预留右侧空间 -->
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 左侧区域 -->
      <div class="left-section">
        <!-- 设备状态 -->
        <div class="panel">
          <div class="panel-header">
            <h3 class="panel-title">设备状态</h3>
          </div>
          <div class="device-list">
            <!-- 摄像头设备 -->
            <div
              class="device-card"
              :class="getDeviceCardClass(device.camera.status)"
            >
              <div class="device-icon">
                <i class="i-mdi-camera"></i>
              </div>
              <div class="device-info">
                <h4 class="device-title">摄像头设备</h4>
                <div
                  class="device-value"
                  :class="getStatusClass(device.camera.status)"
                >
                  {{ getStatusText(device.camera.status) }}
                </div>
              </div>
              <div
                class="device-indicator"
                :class="getIndicatorClass(device.camera.status)"
              ></div>
            </div>

            <!-- 广播设备 -->
            <div
              class="device-card"
              :class="getDeviceCardClass(device.broadcast.status)"
            >
              <div class="device-icon">
                <i class="i-mdi-broadcast"></i>
              </div>
              <div class="device-info">
                <h4 class="device-title">广播设备</h4>
                <div
                  class="device-value"
                  :class="getStatusClass(device.broadcast.status)"
                >
                  {{ getStatusText(device.broadcast.status) }}
                </div>
              </div>
              <div
                class="device-indicator"
                :class="getIndicatorClass(device.broadcast.status)"
              ></div>
            </div>
          </div>
        </div>

        <!-- 环境数据 -->
        <div class="panel">
          <div class="panel-header">
            <h3 class="panel-title">水质监测</h3>
            <div class="time-switch">
              <button
                :class="[
                  'switch-btn',
                  { active: waterQualityTimeRange === 'day' },
                ]"
                @click="waterQualityTimeRange = 'day'"
              >
                当天
              </button>
              <button
                :class="[
                  'switch-btn',
                  { active: waterQualityTimeRange === 'month' },
                ]"
                @click="waterQualityTimeRange = 'month'"
              >
                近一月
              </button>
            </div>
          </div>
          <div class="monitor-grid">
            <div
              class="monitor-item"
              v-for="item in waterQualityData"
              :key="item.name"
            >
              <i :class="getEnvironmentIcon(item.name)"></i>
              <span class="monitor-value">{{ item.value }}</span>
              <span class="monitor-unit">{{ item.unit }}</span>
            </div>
          </div>
          <!-- 环境数据趋势图 -->
          <div class="environment-trend-chart">
            <EChartsComponent :option="environmentTrendOption" />
          </div>
        </div>

        <!-- 虾类养殖数据 -->
        <div class="panel chart-panel">
          <div class="panel-header">
            <h3 class="panel-title">虾类养殖数据</h3>
            <div class="time-switch">
              <button
                :class="[
                  'switch-btn',
                  { active: shrimpDataTimeRange === 'month' },
                ]"
                @click="shrimpDataTimeRange = 'month'"
              >
                近一月
              </button>
            </div>
          </div>
          <div class="chart-container">
            <EChartsComponent :option="shrimpDataOption" />
          </div>
        </div>

        <!-- 视频监控 -->
        <div class="panel h-0 flex-1">
          <h3 class="panel-title">视频监控</h3>
          <div class="video-content">
            <div class="main-video">
              <img
                src="@/assets/images/mainVideoPlaceholder.webp"
                alt="主监控"
                class="video-placeholder"
              />
            </div>
            <div class="sub-videos">
              <div class="sub-video" v-for="i in 3" :key="i">
                <img
                  src="@/assets/images/subVideoPlaceholder.webp"
                  alt="监控"
                  class="video-placeholder"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="center-section flex flex-col">
        <ThreeJSPoolViewer ref="poolViewerRef" />
        <!-- 报警信息 -->
        <div class="panel alarm-info mt-4">
          <h3 class="panel-title">报警信息</h3>
          <div class="alarm-table-wrapper">
            <AlarmTable :alarm-data="alarmData" />
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right-section">
        <!-- 设备监测数据 -->
        <div class="panel workshop-environment">
          <h3 class="panel-title">设备监测数据</h3>
          <div class="environment-display">
            <!-- 供氧机状态 -->
            <div class="environment-item">
              <div class="environment-icon">
                <i class="i-mdi-air-filter text-blue-400"></i>
              </div>
              <div class="environment-info">
                <div class="environment-label">供氧机状态</div>
                <div class="environment-value">
                  {{ equipmentMonitorData.oxygenPumpStatus }}
                </div>
              </div>
            </div>

            <!-- 分割线 -->
            <div class="environment-divider"></div>

            <!-- 当天耗电量 -->
            <div class="environment-item">
              <div class="environment-icon">
                <i class="i-mdi-flash text-yellow-400"></i>
              </div>
              <div class="environment-info">
                <div class="environment-label">当天耗电量</div>
                <div class="environment-value">
                  {{ equipmentMonitorData.powerConsumption }}kWh
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 虾类生长状态 -->
        <div class="panel chart-panel">
          <h3 class="panel-title">虾类生长状态</h3>
          <div class="chart-container">
            <Pie3DChart
              :data="shrimpGrowthData"
              :center-text="{
                title: '总计',
                value: shrimpGrowthTotal,
                unit: '尾',
              }"
              :colors="shrimpGrowthColors"
            />
          </div>
        </div>

        <!-- 池塘管理数据 -->
        <div class="panel chart-panel">
          <div class="panel-header">
            <h3 class="panel-title">池塘管理数据</h3>
            <div class="time-switch">
              <button
                :class="[
                  'switch-btn',
                  { active: pondManagementTimeRange === 'day' },
                ]"
                @click="pondManagementTimeRange = 'day'"
              >
                当天
              </button>
              <button
                :class="[
                  'switch-btn',
                  { active: pondManagementTimeRange === 'month' },
                ]"
                @click="pondManagementTimeRange = 'month'"
              >
                近一月
              </button>
            </div>
          </div>
          <div class="chart-container">
            <EChartsComponent :option="pondManagementOption" />
          </div>
        </div>

        <!-- 养殖时间与投喂数据 -->
        <div class="panel chart-panel">
          <div class="panel-header">
            <h3 class="panel-title">养殖时间与投喂数据</h3>
            <div class="time-switch">
              <button
                :class="[
                  'switch-btn',
                  { active: feedingDataTimeRange === 'day' },
                ]"
                @click="feedingDataTimeRange = 'day'"
              >
                当天
              </button>
              <button
                :class="[
                  'switch-btn',
                  { active: feedingDataTimeRange === 'month' },
                ]"
                @click="feedingDataTimeRange = 'month'"
              >
                近一月
              </button>
            </div>
          </div>
          <div class="chart-container">
            <EChartsComponent :option="feedingDataOption" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from "vue";
import { useRoute } from "vue-router";
import EChartsComponent from "../components/EChartsComponent.vue";
import Pie3DChart from "../components/Pie3DChart.vue";
import AlarmTable from "../components/AlarmTable.vue";
import type { EChartsOption } from "echarts";
import dayjs from "dayjs";
import ThreeJSPoolViewer from "../components/ThreeJSPoolViewer.vue";

const route = useRoute();

// 计算动态标题
const dynamicTitle = computed(() => {
  // 将楼层名字转换为更友好的显示格式
  const floorName = (route.params.floorName as string) || "1栋1层";
  // 例如：将"1栋2层"转换为"银海集团1号楼第二层数据检测信息系统"
  const match = floorName.match(/(\d+)栋(\d+)层/);
  if (match) {
    const building = match[1];
    const floor = match[2];
    const floorText = getFloorText(parseInt(floor));
    return `银海集团${building}号楼第${floorText}层数据检测信息系统`;
  }
  return "银海集团1号楼第一层数据检测信息系统";
});

// 将数字转换为中文
function getFloorText(floor: number): string {
  const floorMap: { [key: number]: string } = {
    1: "一",
    2: "二",
    3: "三",
    4: "四",
    5: "五",
    6: "六",
    7: "七",
    8: "八",
    9: "九",
    10: "十",
  };
  return floorMap[floor] || floor.toString();
}

// 获取ThreeJSPoolViewer的引用
const poolViewerRef = ref();

// 报警数据
const alarmData = ref<
  Array<{
    poolName: string;
    deviceName: string;
    reason: string;
    time: string;
    level: "high" | "medium" | "low";
  }>
>([]);

// 定义事件发射器
// const emit = defineEmits<{
//   "switch-to-homepage": [];
// }>();

// 处理切换到主页的点击事件
// const handleSwitchToHomepage = () => {
//   emit("switch-to-homepage");
// };

// Tooltip参数类型定义
interface TooltipParam {
  axisValue?: string;
  value: number;
  seriesName?: string;
  marker?: string;
}

// 水质监测数据
const waterQualityData = ref([
  { name: "PH值", value: "7.8", unit: "" },
  { name: "水温", value: "28.5", unit: "°C" },
  { name: "溶解氧", value: "6.5", unit: "mg/L" },
  { name: "氨氮", value: "0.3", unit: "mg/L" },
  { name: "盐度", value: "15.2", unit: "‰" },
]);

// 设备监测数据
const equipmentMonitorData = ref({
  oxygenPumpStatus: "运行中",
  powerConsumption: "125.6",
});

// 水质数据图标映射
const getEnvironmentIcon = (name: string) => {
  const iconMap: Record<string, string> = {
    PH值: "i-mdi-test-tube text-green-400",
    水温: "i-mdi-thermometer text-red-400",
    溶解氧: "i-mdi-air-filter text-cyan-400",
    氨氮: "i-mdi-molecule text-purple-400",
    盐度: "i-mdi-grain text-yellow-400",
  };
  return iconMap[name] || "i-mdi-gauge text-gray-400";
};

// 设备状态数据
const device = ref({
  camera: {
    status: "normal", // normal, warning, error
    onlineTime: 24,
    recording: true,
    resolution: "1920x1080",
  },
  broadcast: {
    status: "error", // 设置为异常状态
    volume: 75,
    playing: false,
    connected: true,
  },
});

// 时间范围切换状态
const waterQualityTimeRange = ref<"day" | "month">("day");
const shrimpDataTimeRange = ref<"day" | "month">("month"); // 默认显示近一月
const pondManagementTimeRange = ref<"day" | "month">("day");
const feedingDataTimeRange = ref<"day" | "month">("day");

// 注释：已移除养殖区轮播相关变量

// 养殖区数据
const areaData = ref<
  Record<number, { small: number; medium: number; large: number }>
>({
  1: { small: 1200, medium: 800, large: 400 },
  2: { small: 1150, medium: 850, large: 450 },
  3: { small: 1300, medium: 780, large: 380 },
});

// 生成24小时水质数据
const generate24HourWaterQualityData = () => {
  const hours = [];
  const temperatureData = [];
  const phData = [];
  const oxygenData = [];
  const ammoniaData = [];
  const salinityData = [];

  // 获取当前时间，并设置为整点
  const now = dayjs();
  const currentHour = now.hour();

  // 生成24小时的数据
  for (let i = 23; i >= 0; i--) {
    const hourTime = dayjs()
      .hour(currentHour)
      .minute(0)
      .second(0)
      .subtract(i, "hour");
    hours.push(hourTime.format("HH:00"));

    // 生成各项水质数据（模拟真实波动）
    const hourOffset = i * 0.05;

    // 水温数据 (26-30°C)
    const tempBase = 28.5;
    const tempVariation =
      Math.sin((i / 24) * Math.PI * 2) * 1.5 + (Math.random() - 0.5) * 0.8;
    temperatureData.push(
      Number((tempBase + tempVariation + hourOffset).toFixed(1))
    );

    // pH数据 (7.0-8.5)
    const phBase = 7.8;
    const phVariation =
      Math.sin((i / 24) * Math.PI * 2 + 1) * 0.4 + (Math.random() - 0.5) * 0.2;
    phData.push(Number((phBase + phVariation).toFixed(1)));

    // 溶解氧数据 (5.5-7.5 mg/L)
    const oxygenBase = 6.5;
    const oxygenVariation =
      Math.sin((i / 24) * Math.PI * 2 + 2) * 0.8 + (Math.random() - 0.5) * 0.3;
    oxygenData.push(Number((oxygenBase + oxygenVariation).toFixed(1)));

    // 氨氮数据 (0.1-0.5 mg/L)
    const ammoniaBase = 0.3;
    const ammoniaVariation =
      Math.sin((i / 24) * Math.PI * 2 + 3) * 0.15 + (Math.random() - 0.5) * 0.1;
    ammoniaData.push(Number((ammoniaBase + ammoniaVariation).toFixed(2)));

    // 盐度数据 (14-16‰)
    const salinityBase = 15.2;
    const salinityVariation =
      Math.sin((i / 24) * Math.PI * 2 + 4) * 0.6 + (Math.random() - 0.5) * 0.4;
    salinityData.push(Number((salinityBase + salinityVariation).toFixed(1)));
  }

  return {
    hours,
    temperatureData,
    phData,
    oxygenData,
    ammoniaData,
    salinityData,
  };
};

// 生成近一月水质数据
const generateMonthWaterQualityData = () => {
  const days = [];
  const temperatureData = [];
  const phData = [];
  const oxygenData = [];
  const ammoniaData = [];
  const salinityData = [];

  // 生成近30天的数据
  for (let i = 29; i >= 0; i--) {
    const date = dayjs().subtract(i, "day");
    days.push(date.format("MM/DD"));

    // 生成各项水质数据（模拟真实波动）
    const dayOffset = i * 0.02;

    // 水温数据 (26-30°C)
    const tempBase = 28.5;
    const tempVariation =
      Math.sin((i / 30) * Math.PI * 2) * 2 + (Math.random() - 0.5) * 1;
    temperatureData.push(
      Number((tempBase + tempVariation + dayOffset).toFixed(1))
    );

    // pH数据 (7.0-8.5)
    const phBase = 7.8;
    const phVariation =
      Math.sin((i / 30) * Math.PI * 2 + 1) * 0.5 + (Math.random() - 0.5) * 0.3;
    phData.push(Number((phBase + phVariation).toFixed(1)));

    // 溶解氧数据 (5.5-7.5 mg/L)
    const oxygenBase = 6.5;
    const oxygenVariation =
      Math.sin((i / 30) * Math.PI * 2 + 2) * 1 + (Math.random() - 0.5) * 0.5;
    oxygenData.push(Number((oxygenBase + oxygenVariation).toFixed(1)));

    // 氨氮数据 (0.1-0.5 mg/L)
    const ammoniaBase = 0.3;
    const ammoniaVariation =
      Math.sin((i / 30) * Math.PI * 2 + 3) * 0.2 + (Math.random() - 0.5) * 0.15;
    ammoniaData.push(Number((ammoniaBase + ammoniaVariation).toFixed(2)));

    // 盐度数据 (14-16‰)
    const salinityBase = 15.2;
    const salinityVariation =
      Math.sin((i / 30) * Math.PI * 2 + 4) * 0.8 + (Math.random() - 0.5) * 0.6;
    salinityData.push(Number((salinityBase + salinityVariation).toFixed(1)));
  }

  return {
    hours: days, // 保持接口一致性
    temperatureData,
    phData,
    oxygenData,
    ammoniaData,
    salinityData,
  };
};

// 生成虾类生长状态数据
const generateShrimpGrowthData = () => {
  const shrimpGrowthStages = [
    {
      name: "虾苗",
      value: 1200,
      itemStyle: {
        color: "#00d4ff",
      },
    },
    {
      name: "15cm",
      value: 800,
      itemStyle: {
        color: "#ffd93d",
      },
    },
    {
      name: "30cm",
      value: 450,
      itemStyle: {
        color: "#ff6b6b",
      },
    },
    {
      name: "45cm",
      value: 180,
      itemStyle: {
        color: "#4ecdc4",
      },
    },
  ];

  return shrimpGrowthStages;
};

// 虾类生长状态数据
const shrimpGrowthData = computed(() => generateShrimpGrowthData());

// 虾类生长状态颜色配置
const shrimpGrowthColors = ["#00d4ff", "#ffd93d", "#ff6b6b", "#4ecdc4"];

// 计算虾类总数
const shrimpGrowthTotal = computed(() => {
  return shrimpGrowthData.value.reduce((sum, item) => sum + item.value, 0);
});

// 生成虾类数据图表（左侧新增的图表）
const shrimpDataOption = computed<EChartsOption>(() => {
  // 近一月数据 - 显示30天的虾类生长状态变化
  const days = [];
  const shrimpSeedData = []; // 虾苗
  const shrimp15Data = []; // 15cm
  const shrimp30Data = []; // 30cm
  const shrimp45Data = []; // 45cm

  for (let i = 29; i >= 0; i--) {
    const date = dayjs().subtract(i, "day");
    days.push(date.format("MM/DD"));

    // 模拟虾类生长过程（虾苗逐渐减少，大虾逐渐增加）
    const progress = (29 - i) / 29; // 0到1的进度

    shrimpSeedData.push(Math.round(1200 - progress * 400 + Math.random() * 50));
    shrimp15Data.push(Math.round(600 + progress * 200 + Math.random() * 30));
    shrimp30Data.push(Math.round(300 + progress * 150 + Math.random() * 20));
    shrimp45Data.push(Math.round(100 + progress * 80 + Math.random() * 10));
  }

  return {
    backgroundColor: "transparent",
    grid: { left: 50, right: 20, top: 60, bottom: 20 },
    legend: {
      data: ["虾苗", "15cm", "30cm", "45cm"],
      textStyle: { color: "#66ccff", fontSize: 12 },
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#66ccff",
      borderWidth: 1,
      textStyle: { color: "#66ccff", fontSize: 12 },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        let result = `${(paramArray[0] as TooltipParam).axisValue}<br/>`;
        paramArray.forEach((param: TooltipParam) => {
          result += `${param.marker}${param.seriesName}: ${param.value}尾<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      type: "category",
      data: days,
      axisLine: { lineStyle: { color: "#66ccff" } },
      axisLabel: { color: "#66ccff", fontSize: 10, interval: 4 },
    },
    yAxis: {
      type: "value",
      name: "数量(尾)",
      nameTextStyle: { color: "#66ccff", fontSize: 10 },
      axisLine: { lineStyle: { color: "#66ccff" } },
      axisLabel: { color: "#66ccff", fontSize: 10 },
      splitLine: { lineStyle: { color: "rgba(102, 204, 255, 0.2)" } },
    },
    series: [
      {
        name: "虾苗",
        type: "line",
        data: shrimpSeedData,
        smooth: true,
        lineStyle: { color: "#00d4ff", width: 2 },
        itemStyle: { color: "#00d4ff" },
      },
      {
        name: "15cm",
        type: "line",
        data: shrimp15Data,
        smooth: true,
        lineStyle: { color: "#ffd93d", width: 2 },
        itemStyle: { color: "#ffd93d" },
      },
      {
        name: "30cm",
        type: "line",
        data: shrimp30Data,
        smooth: true,
        lineStyle: { color: "#ff6b6b", width: 2 },
        itemStyle: { color: "#ff6b6b" },
      },
      {
        name: "45cm",
        type: "line",
        data: shrimp45Data,
        smooth: true,
        lineStyle: { color: "#4ecdc4", width: 2 },
        itemStyle: { color: "#4ecdc4" },
      },
    ],
  };
});

// 水质监测趋势图表配置
const environmentTrendOption = computed<EChartsOption>(() => {
  const data =
    waterQualityTimeRange.value === "day"
      ? generate24HourWaterQualityData()
      : generateMonthWaterQualityData();

  return {
    backgroundColor: "transparent",
    grid: { left: 30, right: 30, top: 60, bottom: 30 },
    legend: {
      data: ["PH值", "水温", "溶解氧", "氨氮", "盐度"],
      textStyle: {
        color: "#66ccff",
        fontSize: 12,
      },
      top: 0,
      left: 0,
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#66ccff",
      borderWidth: 1,
      textStyle: {
        color: "#66ccff",
        fontSize: 12,
      },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        let result = `${(paramArray[0] as TooltipParam).axisValue}<br/>`;
        paramArray.forEach((param: TooltipParam) => {
          const units = ["", "°C", "mg/L", "mg/L", "‰"];
          const unitIndex = ["PH值", "水温", "溶解氧", "氨氮", "盐度"].indexOf(
            param.seriesName || ""
          );
          const unit = units[unitIndex] || "";
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      type: "category",
      data: data.hours,
      axisLine: { lineStyle: { color: "#66ccff" } },
      axisLabel: {
        color: "#66ccff",
        fontSize: 10,
        interval: waterQualityTimeRange.value === "day" ? 2 : 4, // 当天每3小时，月度每5天显示标签
        margin: 8,
      },
    },
    yAxis: [
      {
        type: "value",
        nameTextStyle: { color: "#66ccff", fontSize: 10 },
        axisLine: { lineStyle: { color: "#66ccff" } },
        axisLabel: { color: "#66ccff", fontSize: 10 },
        splitLine: { lineStyle: { color: "rgba(102, 204, 255, 0.2)" } },
      },
      {
        type: "value",
        nameTextStyle: { color: "#66ccff", fontSize: 10 },
        axisLine: { lineStyle: { color: "#66ccff" } },
        axisLabel: { color: "#66ccff", fontSize: 10 },
        splitLine: { show: false },
      },
    ],
    series: [
      {
        name: "PH值",
        type: "line",
        data: data.phData,
        smooth: true,
        lineStyle: { color: "#ffd93d", width: 2 },
        itemStyle: { color: "#ffd93d" },
        yAxisIndex: 0,
      },
      {
        name: "水温",
        type: "line",
        data: data.temperatureData,
        smooth: true,
        lineStyle: { color: "#ff6b6b", width: 2 },
        itemStyle: { color: "#ff6b6b" },
        yAxisIndex: 0,
      },
      {
        name: "溶解氧",
        type: "line",
        data: data.oxygenData,
        smooth: true,
        lineStyle: { color: "#00ff88", width: 2 },
        itemStyle: { color: "#00ff88" },
        yAxisIndex: 0,
      },
      {
        name: "氨氮",
        type: "line",
        data: data.ammoniaData,
        smooth: true,
        lineStyle: { color: "#8a2be2", width: 2 },
        itemStyle: { color: "#8a2be2" },
        yAxisIndex: 1,
      },
      {
        name: "盐度",
        type: "line",
        data: data.salinityData,
        smooth: true,
        lineStyle: { color: "#66ccff", width: 2 },
        itemStyle: { color: "#66ccff" },
        yAxisIndex: 1,
      },
    ],
  };
});

// 池塘管理数据图表配置
const pondManagementOption = computed<EChartsOption>(() => {
  const timeLabels = [];
  const waterLevelData = [];
  const drainageCountData = [];

  if (pondManagementTimeRange.value === "day") {
    // 生成当天24小时的数据
    for (let i = 0; i < 24; i++) {
      timeLabels.push(`${i.toString().padStart(2, "0")}:00`);

      // 水位数据 (1.2-1.5m)
      const waterLevelBase = 1.35;
      const waterLevelVariation =
        Math.sin((i / 24) * Math.PI * 2) * 0.15 + (Math.random() - 0.5) * 0.05;
      waterLevelData.push(
        Number((waterLevelBase + waterLevelVariation).toFixed(2))
      );

      // 排水次数（累计）
      const drainageBase = Math.floor(i / 8); // 每8小时可能排水一次
      const drainageVariation = Math.random() < 0.3 ? 1 : 0;
      drainageCountData.push(drainageBase + drainageVariation);
    }
  } else {
    // 生成近一月的数据
    for (let i = 29; i >= 0; i--) {
      const date = dayjs().subtract(i, "day");
      timeLabels.push(date.format("MM/DD"));

      // 水位数据 (1.2-1.5m) - 月度数据波动更大
      const waterLevelBase = 1.35;
      const waterLevelVariation =
        Math.sin((i / 30) * Math.PI * 2) * 0.2 + (Math.random() - 0.5) * 0.1;
      waterLevelData.push(
        Number((waterLevelBase + waterLevelVariation).toFixed(2))
      );

      // 排水次数（每天的排水次数）
      const dailyDrainageCount = Math.floor(Math.random() * 4) + 1; // 1-4次
      drainageCountData.push(dailyDrainageCount);
    }
  }

  return {
    backgroundColor: "transparent",
    grid: { left: 50, right: 50, top: 40, bottom: 30 },
    legend: {
      data: ["池子水位", "排水次数"],
      textStyle: {
        color: "#66ccff",
        fontSize: 12,
      },
      top: 5,
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#66ccff",
      borderWidth: 1,
      textStyle: {
        color: "#66ccff",
        fontSize: 12,
      },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        let result = `${(paramArray[0] as TooltipParam).axisValue}<br/>`;
        paramArray.forEach((param: TooltipParam) => {
          const units = ["m", "次"];
          const unitIndex = ["池子水位", "排水次数"].indexOf(
            param.seriesName || ""
          );
          const unit = units[unitIndex] || "";
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      type: "category",
      data: timeLabels,
      axisLine: { lineStyle: { color: "#66ccff" } },
      axisLabel: {
        color: "#66ccff",
        fontSize: 10,
        interval: pondManagementTimeRange.value === "day" ? 2 : 4,
        margin: 8,
      },
    },
    yAxis: [
      {
        type: "value",
        name: "水位(m)",
        nameTextStyle: { color: "#66ccff", fontSize: 10 },
        axisLine: { lineStyle: { color: "#66ccff" } },
        axisLabel: { color: "#66ccff", fontSize: 10 },
        splitLine: { lineStyle: { color: "rgba(102, 204, 255, 0.2)" } },
      },
      {
        type: "value",
        name: "次数",
        nameTextStyle: { color: "#66ccff", fontSize: 10 },
        axisLine: { lineStyle: { color: "#66ccff" } },
        axisLabel: { color: "#66ccff", fontSize: 10 },
        splitLine: { show: false },
      },
    ],
    series: [
      {
        name: "池子水位",
        type: "line",
        data: waterLevelData,
        smooth: true,
        lineStyle: { color: "#00d4ff", width: 2 },
        itemStyle: { color: "#00d4ff" },
        yAxisIndex: 0,
      },
      {
        name: "排水次数",
        type: "bar",
        data: drainageCountData,
        itemStyle: { color: "#ffd93d" },
        yAxisIndex: 1,
        barWidth: "60%",
      },
    ],
  };
});

// 养殖时间与投喂数据图表配置
const feedingDataOption = computed<EChartsOption>(() => {
  const timeLabels = [];
  const feedingCountData = [];
  const feedingWeightData = [];
  const breedingTimeData = [];

  if (feedingDataTimeRange.value === "day") {
    // 生成当天24小时的数据
    for (let i = 0; i < 24; i++) {
      timeLabels.push(`${i.toString().padStart(2, "0")}:00`);

      // 投喂次数（一天通常3-4次）
      let feedingCount = 0;
      if (i >= 6 && i <= 8) feedingCount = 1; // 早上 6-8点
      if (i >= 12 && i <= 14) feedingCount = 1; // 中午 12-14点
      if (i >= 18 && i <= 20) feedingCount = 1; // 下午 18-20点
      if (i >= 22 || i <= 2) feedingCount = 1; // 晚上 22-2点
      feedingCountData.push(feedingCount);

      // 投喂重量（kg）
      const feedingWeight = feedingCount > 0 ? 15 + Math.random() * 10 : 0;
      feedingWeightData.push(Number(feedingWeight.toFixed(1)));

      // 养殖时间（天数，假设当前是第45天）
      const breedingDays = 45;
      breedingTimeData.push(breedingDays);
    }
  } else {
    // 生成近一月的数据
    for (let i = 29; i >= 0; i--) {
      const date = dayjs().subtract(i, "day");
      timeLabels.push(date.format("MM/DD"));

      // 每天投喂次数（3-4次）
      const dailyFeedingCount = Math.floor(Math.random() * 2) + 3; // 3-4次
      feedingCountData.push(dailyFeedingCount);

      // 每天投喂总重量（kg）
      const dailyFeedingWeight = dailyFeedingCount * (15 + Math.random() * 10);
      feedingWeightData.push(Number(dailyFeedingWeight.toFixed(1)));

      // 养殖时间（逐渐增加）
      const breedingDays = 15 + (29 - i); // 从第15天开始到第44天
      breedingTimeData.push(breedingDays);
    }
  }

  return {
    backgroundColor: "transparent",
    grid: { left: 50, right: 50, top: 40, bottom: 30 },
    legend: {
      data: ["投喂次数", "投喂重量", "养殖天数"],
      textStyle: {
        color: "#66ccff",
        fontSize: 12,
      },
      top: 5,
    },
    tooltip: {
      trigger: "axis",
      backgroundColor: "rgba(0, 0, 0, 0.8)",
      borderColor: "#66ccff",
      borderWidth: 1,
      textStyle: {
        color: "#66ccff",
        fontSize: 12,
      },
      formatter: (params: unknown) => {
        const paramArray = Array.isArray(params) ? params : [params];
        let result = `${(paramArray[0] as TooltipParam).axisValue}<br/>`;
        paramArray.forEach((param: TooltipParam) => {
          const units = ["次", "kg", "天"];
          const unitIndex = ["投喂次数", "投喂重量", "养殖天数"].indexOf(
            param.seriesName || ""
          );
          const unit = units[unitIndex] || "";
          result += `${param.marker}${param.seriesName}: ${param.value}${unit}<br/>`;
        });
        return result;
      },
    },
    xAxis: {
      type: "category",
      data: timeLabels,
      axisLine: { lineStyle: { color: "#66ccff" } },
      axisLabel: {
        color: "#66ccff",
        fontSize: 10,
        interval: feedingDataTimeRange.value === "day" ? 2 : 4,
        margin: 8,
      },
    },
    yAxis: [
      {
        type: "value",
        name: "次数/重量",
        nameTextStyle: { color: "#66ccff", fontSize: 10 },
        axisLine: { lineStyle: { color: "#66ccff" } },
        axisLabel: { color: "#66ccff", fontSize: 10 },
        splitLine: { lineStyle: { color: "rgba(102, 204, 255, 0.2)" } },
      },
      {
        type: "value",
        name: "天数",
        nameTextStyle: { color: "#66ccff", fontSize: 10 },
        axisLine: { lineStyle: { color: "#66ccff" } },
        axisLabel: { color: "#66ccff", fontSize: 10 },
        splitLine: { show: false },
      },
    ],
    series: [
      {
        name: "投喂次数",
        type: "bar",
        data: feedingCountData,
        itemStyle: { color: "#ff6b6b" },
        yAxisIndex: 0,
        barWidth: "30%",
      },
      {
        name: "投喂重量",
        type: "bar",
        data: feedingWeightData,
        itemStyle: { color: "#ffd93d" },
        yAxisIndex: 0,
        barWidth: "30%",
      },
      {
        name: "养殖天数",
        type: "line",
        data: breedingTimeData,
        smooth: true,
        lineStyle: { color: "#00ff88", width: 2 },
        itemStyle: { color: "#00ff88" },
        yAxisIndex: 1,
      },
    ],
  };
});

// 更新报警数据
const updateAlarmData = () => {
  if (poolViewerRef.value && poolViewerRef.value.getAlarmData) {
    alarmData.value = poolViewerRef.value.getAlarmData();
  }
};

// 生成所有区域数据（为池子数据计算提供基础数据）
const generateAllAreaData = () => {
  for (let i = 1; i <= 18; i++) {
    if (!areaData.value[i]) {
      areaData.value[i] = {
        small: Math.floor(Math.random() * 500) + 1000,
        medium: Math.floor(Math.random() * 300) + 700,
        large: Math.floor(Math.random() * 200) + 300,
      };
    }
  }
};

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    normal: "正常",
    warning: "警告",
    error: "故障",
  };
  return statusMap[status] || "未知";
};

// 获取设备卡片样式类
const getDeviceCardClass = (status: string) => {
  return `device-card-${status}`;
};

// 获取状态样式类
const getStatusClass = (status: string) => {
  return `status-${status}`;
};

// 获取指示器样式类
const getIndicatorClass = (status: string) => {
  return `indicator-${status}`;
};

// 更新设备监测数据
const updateEquipmentData = () => {
  // 耗电量在120-130kWh之间波动
  const basePower = 125.6;
  const powerVariation = (Math.random() - 0.5) * 10; // ±5kWh
  equipmentMonitorData.value.powerConsumption = (
    basePower + powerVariation
  ).toFixed(1);

  // 随机更新供氧机状态
  const statuses = ["运行中", "待机", "维护中"];
  if (Math.random() < 0.1) {
    // 10%概率改变状态
    equipmentMonitorData.value.oxygenPumpStatus =
      statuses[Math.floor(Math.random() * statuses.length)];
  }
};

// 更新水质监测数据
const updateWaterQualityData = () => {
  waterQualityData.value.forEach((item) => {
    const currentValue = parseFloat(item.value);
    let variation = 0;

    switch (item.name) {
      case "PH值":
        variation = (Math.random() - 0.5) * 0.2; // ±0.1
        item.value = Math.max(
          7.0,
          Math.min(8.5, currentValue + variation)
        ).toFixed(1);
        break;
      case "水温":
        variation = (Math.random() - 0.5) * 1; // ±0.5°C
        item.value = Math.max(
          26,
          Math.min(30, currentValue + variation)
        ).toFixed(1);
        break;
      case "溶解氧":
        variation = (Math.random() - 0.5) * 0.4; // ±0.2mg/L
        item.value = Math.max(
          5.5,
          Math.min(7.5, currentValue + variation)
        ).toFixed(1);
        break;
      case "氨氮":
        variation = (Math.random() - 0.5) * 0.1; // ±0.05mg/L
        item.value = Math.max(
          0.1,
          Math.min(0.5, currentValue + variation)
        ).toFixed(2);
        break;
      case "盐度":
        variation = (Math.random() - 0.5) * 0.6; // ±0.3‰
        item.value = Math.max(
          14,
          Math.min(16, currentValue + variation)
        ).toFixed(1);
        break;
    }
  });
};

// 更新设备状态数据
const updateDeviceStatus = () => {
  // 随机更新摄像头状态
  if (Math.random() < 0.05) {
    // 5%概率改变状态
    const statuses = ["normal", "warning", "error"];
    device.value.camera.status =
      statuses[Math.floor(Math.random() * statuses.length)];
  }

  // 更新在线时间
  device.value.camera.onlineTime = Math.floor(Math.random() * 48) + 1;

  // 随机更新广播状态
  if (Math.random() < 0.05) {
    // 5%概率改变状态
    const statuses = ["normal", "warning", "error"];
    device.value.broadcast.status =
      statuses[Math.floor(Math.random() * statuses.length)];
  }

  // 随机更新音量
  device.value.broadcast.volume = Math.floor(Math.random() * 100);

  // 随机更新播放状态
  if (Math.random() < 0.1) {
    // 10%概率改变播放状态
    device.value.broadcast.playing = !device.value.broadcast.playing;
  }
};

onMounted(() => {
  generateAllAreaData();

  // 延迟更新报警数据，确保ThreeJSPoolViewer已经加载完成
  setTimeout(() => {
    updateAlarmData();
    // 每30秒更新一次报警数据
    setInterval(updateAlarmData, 30000);
  }, 2000);

  // 每30秒更新一次设备监测数据
  setInterval(updateEquipmentData, 30000);

  // 每10秒更新一次水质监测数据
  setInterval(updateWaterQualityData, 10000);

  // 每20秒更新一次设备状态数据
  setInterval(updateDeviceStatus, 20000);
});
</script>

<style scoped>
.aquaculture-dashboard {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
  color: #66ccff;
  font-family: "Microsoft YaHei", sans-serif;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

.aquaculture-dashboard::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
      circle at 20% 20%,
      rgba(102, 204, 255, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 80% 80%,
      rgba(0, 255, 136, 0.1) 0%,
      transparent 50%
    ),
    radial-gradient(
      circle at 40% 60%,
      rgba(255, 107, 107, 0.1) 0%,
      transparent 50%
    );
  pointer-events: none;
}

/* 顶部标题栏 */
.header-section {
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40px;
  background: linear-gradient(
    90deg,
    rgba(102, 204, 255, 0.1) 0%,
    rgba(102, 204, 255, 0.05) 50%,
    rgba(102, 204, 255, 0.1) 100%
  );
  border-bottom: 2px solid rgba(102, 204, 255, 0.3);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 10;
}

.header-section::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #66ccff 50%,
    transparent 100%
  );
  box-shadow: 0 0 10px #66ccff;
}

.seed-date {
  font-size: 16px;
  color: #66ccff;
  text-shadow: 0 0 10px rgba(102, 204, 255, 0.8);
}

.main-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  letter-spacing: 2px;
  margin: 0;
}

/* 主内容区域 */
.main-content {
  height: 0;
  flex: 1;
  display: flex;
  gap: 20px;
  padding: 20px;
  overflow: hidden; /* 防止整体滚动 */
}

.left-section,
.right-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%; /* 确保占满高度 */
  width: 400px;
}

.center-section {
  flex: 1;
  width: 0;
}

/* 面板通用样式 */
.panel {
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.1) 0%,
    rgba(102, 204, 255, 0.05) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 12px;
  padding: 10px;
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 面板头部样式 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

/* 时间切换按钮样式 */
.time-switch {
  display: flex;
  gap: 4px;
  background: rgba(102, 204, 255, 0.1);
  border-radius: 6px;
  padding: 2px;
  border: 1px solid rgba(102, 204, 255, 0.2);
}

.switch-btn {
  padding: 4px 8px;
  font-size: 10px;
  color: #66ccff;
  background: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.switch-btn:hover {
  background: rgba(102, 204, 255, 0.2);
  color: #fff;
}

.switch-btn.active {
  background: linear-gradient(135deg, #66ccff 0%, #33aaff 100%);
  color: #000;
  font-weight: bold;
  box-shadow: 0 0 8px rgba(102, 204, 255, 0.5);
}

.panel::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #66ccff 50%,
    transparent 100%
  );
  box-shadow: 0 0 10px #66ccff;
}

.panel-title {
  font-size: 18px;
  font-weight: bold;
  color: #66ccff;
  margin: 0 0 10px 0;
  text-shadow: 0 0 10px rgba(102, 204, 255, 0.8);
  text-align: center;
  flex-shrink: 0; /* 标题不收缩 */
}

/* 环境数据 */
.monitor-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px; /* 为趋势图留出空间 */
}

.monitor-item {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.monitor-label {
  color: #00ff88;
  margin-bottom: 8px;
}

.monitor-value {
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.monitor-unit {
  font-size: 12px;
  color: rgba(0, 255, 136, 0.7);
}

/* 环境数据趋势图 */
.environment-trend-chart {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

/* 设备状态样式 */
.device-list {
  display: flex;
  gap: 8px;
}

.device-card {
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.08) 0%,
    rgba(102, 204, 255, 0.03) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 8px;
  padding: 8px 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 60px;
}

.device-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(102, 204, 255, 0.1) 50%,
    transparent 100%
  );
  box-shadow: inset 0 0 8px rgba(102, 204, 255, 0.2);
  transition: all 0.3s ease;
}

/* 设备图标 */
.device-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.2) 0%,
    rgba(102, 204, 255, 0.1) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.4);
  border-radius: 50%;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.device-icon i {
  color: #66ccff;
  transition: all 0.3s ease;
  font-size: 16px;
}

/* 设备信息 */
.device-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
  position: relative;
  z-index: 2;
}

.device-title {
  font-size: 13px;
  font-weight: bold;
  color: #66ccff;
  margin: 0;
  transition: all 0.3s ease;
  line-height: 1.2;
}

.device-value {
  font-size: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  text-shadow: 0 0 6px currentColor;
  line-height: 1.2;
}

/* 设备指示器 */
.device-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.device-indicator::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 100%;
  height: 100%;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* 正常状态样式 - 漂亮的绿色 */
.device-card-normal {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 136, 0.15) 0%,
    rgba(0, 255, 136, 0.08) 100%
  );
  border-color: rgba(0, 255, 136, 0.4);
  box-shadow: 0 0 20px rgba(0, 255, 136, 0.2);
}

.device-card-normal::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(0, 255, 136, 0.15) 50%,
    transparent 100%
  );
  box-shadow: inset 0 0 12px rgba(0, 255, 136, 0.3);
}

.device-card-normal .device-icon {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 136, 0.25) 0%,
    rgba(0, 255, 136, 0.15) 100%
  );
  border-color: rgba(0, 255, 136, 0.6);
  box-shadow: 0 0 15px rgba(0, 255, 136, 0.4);
}

.device-card-normal .device-icon i {
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.device-card-normal .device-title {
  color: #00ff88;
  text-shadow: 0 0 8px rgba(0, 255, 136, 0.6);
}

.status-normal {
  color: #00ff88;
  text-shadow: 0 0 12px rgba(0, 255, 136, 0.8);
}

.indicator-normal {
  background: #00ff88;
  box-shadow: 0 0 12px rgba(0, 255, 136, 0.8);
}

.indicator-normal::before {
  background: rgba(0, 255, 136, 0.3);
}

/* 警告状态样式 - 橙黄色 */
.device-card-warning {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.15) 0%,
    rgba(255, 193, 7, 0.08) 100%
  );
  border-color: rgba(255, 193, 7, 0.4);
  box-shadow: 0 0 20px rgba(255, 193, 7, 0.2);
  animation: warningGlow 2s ease-in-out infinite alternate;
}

.device-card-warning::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 193, 7, 0.15) 50%,
    transparent 100%
  );
  box-shadow: inset 0 0 12px rgba(255, 193, 7, 0.3);
}

.device-card-warning .device-icon {
  background: linear-gradient(
    135deg,
    rgba(255, 193, 7, 0.25) 0%,
    rgba(255, 193, 7, 0.15) 100%
  );
  border-color: rgba(255, 193, 7, 0.6);
  box-shadow: 0 0 15px rgba(255, 193, 7, 0.4);
}

.device-card-warning .device-icon i {
  color: #ffc107;
  text-shadow: 0 0 10px rgba(255, 193, 7, 0.8);
}

.device-card-warning .device-title {
  color: #ffc107;
  text-shadow: 0 0 8px rgba(255, 193, 7, 0.6);
}

.status-warning {
  color: #ffc107;
  text-shadow: 0 0 12px rgba(255, 193, 7, 0.8);
}

.indicator-warning {
  background: #ffc107;
  box-shadow: 0 0 12px rgba(255, 193, 7, 0.8);
  animation: warningPulse 1.5s ease-in-out infinite;
}

.indicator-warning::before {
  background: rgba(255, 193, 7, 0.3);
}

/* 错误状态样式 - 红色 */
.device-card-error {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 107, 0.15) 0%,
    rgba(255, 107, 107, 0.08) 100%
  );
  border-color: rgba(255, 107, 107, 0.4);
  box-shadow: 0 0 20px rgba(255, 107, 107, 0.2);
  animation: errorGlow 1s ease-in-out infinite alternate;
}

.device-card-error::before {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 107, 107, 0.15) 50%,
    transparent 100%
  );
  box-shadow: inset 0 0 12px rgba(255, 107, 107, 0.3);
}

.device-card-error .device-icon {
  background: linear-gradient(
    135deg,
    rgba(255, 107, 107, 0.25) 0%,
    rgba(255, 107, 107, 0.15) 100%
  );
  border-color: rgba(255, 107, 107, 0.6);
  box-shadow: 0 0 15px rgba(255, 107, 107, 0.4);
}

.device-card-error .device-icon i {
  color: #ff6b6b;
  text-shadow: 0 0 10px rgba(255, 107, 107, 0.8);
}

.device-card-error .device-title {
  color: #ff6b6b;
  text-shadow: 0 0 8px rgba(255, 107, 107, 0.6);
}

.status-error {
  color: #ff6b6b;
  text-shadow: 0 0 12px rgba(255, 107, 107, 0.8);
}

.indicator-error {
  background: #ff6b6b;
  box-shadow: 0 0 12px rgba(255, 107, 107, 0.8);
  animation: errorPulse 0.8s ease-in-out infinite;
}

.indicator-error::before {
  background: rgba(255, 107, 107, 0.3);
}

/* 动画效果 */
@keyframes warningGlow {
  0% {
    box-shadow: 0 0 20px rgba(255, 193, 7, 0.2);
  }
  100% {
    box-shadow: 0 0 30px rgba(255, 193, 7, 0.4);
  }
}

@keyframes errorGlow {
  0% {
    box-shadow: 0 0 20px rgba(255, 107, 107, 0.2);
  }
  100% {
    box-shadow: 0 0 30px rgba(255, 107, 107, 0.4);
  }
}

@keyframes warningPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.7;
  }
}

@keyframes errorPulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.6;
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
}

/* 设备卡片悬停效果 */
.device-card:hover {
  transform: translateY(-2px);
}

.device-card-normal:hover {
  box-shadow: 0 4px 25px rgba(0, 255, 136, 0.3);
}

.device-card-warning:hover {
  box-shadow: 0 4px 25px rgba(255, 193, 7, 0.3);
}

.device-card-error:hover {
  box-shadow: 0 4px 25px rgba(255, 107, 107, 0.3);
}

/* 24小时监测数据 */
.monitoring-content {
  text-align: center;
  flex: 1; /* 占用剩余空间 */
  height: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.current-area {
  font-size: 20px;
  font-weight: bold;
  color: #ffd93d;
  text-shadow: 0 0 15px rgba(255, 217, 61, 0.8);
  margin-bottom: 20px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.shrimp-data {
  display: flex;
  flex-direction: column;
  gap: 15px; /* 增加间距 */
  flex: 1; /* 占用剩余空间 */
}

.shrimp-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px; /* 增加内边距 */
  background: linear-gradient(
    90deg,
    rgba(255, 217, 61, 0.1) 0%,
    rgba(255, 217, 61, 0.05) 100%
  );
  border: 1px solid rgba(255, 217, 61, 0.3);
  border-radius: 6px;
}

.shrimp-label {
  font-size: 14px;
  color: #ffd93d;
}

.shrimp-count {
  font-size: 16px;
  font-weight: bold;
  color: #ffd93d;
  text-shadow: 0 0 8px rgba(255, 217, 61, 0.6);
}

/* 视频监控 */
.video-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
  flex: 1; /* 占用剩余空间 */
  height: 0;
}

.main-video {
  flex: 2;
  height: 0;
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 8px;
  overflow: hidden;
}

.sub-videos {
  flex: 1;
  height: 0;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
}

.sub-video {
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 6px;
  overflow: hidden;
}

.video-placeholder {
  width: 100%;
  height: 100%;
  object-fit: cover;
  filter: brightness(0.8) contrast(1.2);
}

/* 池子显示区域 */
.pools-display {
  flex: 2;
}

.pools-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 12px;
  flex: 1; /* 占用剩余空间 */
}

.pool-item {
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.1) 0%,
    rgba(102, 204, 255, 0.05) 100%
  );
  border: 2px solid rgba(102, 204, 255, 0.3);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.pool-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(102, 204, 255, 0.1) 50%,
    transparent 70%
  );
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.pool-item:hover::before {
  transform: translateX(100%);
}

.pool-item:hover {
  border-color: #66ccff;
  box-shadow: 0 0 20px rgba(102, 204, 255, 0.5);
  transform: scale(1.05);
}

.pool-item.active {
  background: linear-gradient(
    135deg,
    rgba(0, 255, 136, 0.2) 0%,
    rgba(0, 255, 136, 0.1) 100%
  );
  border-color: #00ff88;
  box-shadow: 0 0 25px rgba(0, 255, 136, 0.6);
  animation: activePool 2s infinite;
}

@keyframes activePool {
  0%,
  100% {
    box-shadow: 0 0 25px rgba(0, 255, 136, 0.6);
  }
  50% {
    box-shadow: 0 0 35px rgba(0, 255, 136, 0.8);
  }
}

.pool-number {
  font-size: 18px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 10px rgba(102, 204, 255, 0.8);
  margin-bottom: 4px;
}

.pool-item.active .pool-number {
  color: #00ff88;
  text-shadow: 0 0 10px rgba(0, 255, 136, 0.8);
}

.pool-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
  text-shadow: none;
}

.pool-status.normal {
  background: rgba(0, 255, 136, 0.2);
  color: #00ff88;
  border: 1px solid rgba(0, 255, 136, 0.5);
}

.pool-status.warning {
  background: rgba(255, 107, 107, 0.2);
  color: #ff6b6b;
  border: 1px solid rgba(255, 107, 107, 0.5);
  animation: warning 1.5s infinite;
}

@keyframes warning {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 报警信息表格 */
.alarm-info {
  flex: 1;
  height: 0;
}

.alarm-table-wrapper {
  flex: 1; /* 占用剩余空间 */
  display: flex;
  flex-direction: column;
}

/* 车间环境监测 */
.workshop-environment {
  flex-shrink: 0;
  height: auto;
  min-height: 120px;
}

.environment-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  gap: 20px;
}

.environment-item {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  flex: 1;
}

.environment-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(
    135deg,
    rgba(102, 204, 255, 0.15) 0%,
    rgba(102, 204, 255, 0.08) 100%
  );
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
}

.environment-icon::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(102, 204, 255, 0.2) 0%,
    transparent 70%
  );
  animation: iconGlow 2s ease-in-out infinite alternate;
}

@keyframes iconGlow {
  0% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.environment-icon i {
  font-size: 20px;
  position: relative;
  z-index: 1;
}

.environment-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.environment-label {
  font-size: 14px;
  color: #66ccff;
  opacity: 0.8;
}

.environment-value {
  font-size: 24px;
  font-weight: bold;
  color: #00ff88;
  text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
  animation: valueGlow 3s ease-in-out infinite;
}

@keyframes valueGlow {
  0%,
  100% {
    text-shadow: 0 0 15px rgba(0, 255, 136, 0.8);
  }
  50% {
    text-shadow: 0 0 25px rgba(0, 255, 136, 1);
  }
}

.environment-divider {
  width: 2px;
  height: 60px;
  background: linear-gradient(
    to bottom,
    transparent 0%,
    #66ccff 20%,
    #66ccff 80%,
    transparent 100%
  );
  position: relative;
  flex-shrink: 0;
}

.environment-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: #66ccff;
  border-radius: 50%;
  box-shadow: 0 0 10px #66ccff;
  animation: dividerPulse 2s ease-in-out infinite;
}

@keyframes dividerPulse {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0.7;
  }
}

/* 图表面板 */
.chart-panel {
  flex: 1;
}

.chart-container {
  flex: 1; /* 占用剩余空间 */
}

/* 环境数据趋势图面板 */
.environment-trend-panel {
  flex: 0.8; /* 设置合适的高度比例 */
  min-height: 200px; /* 最小高度 */
}

/* 加载动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.panel {
  animation: fadeInUp 0.6s ease-out;
}

.panel:nth-child(1) {
  animation-delay: 0.1s;
}
.panel:nth-child(2) {
  animation-delay: 0.2s;
}
.panel:nth-child(3) {
  animation-delay: 0.3s;
}

/* 数据更新动画 */
@keyframes dataUpdate {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.monitor-value {
  transition: all 0.3s ease;
}

.monitor-value:hover {
  animation: dataUpdate 0.6s ease;
}

/* 图表容器动画 */
.chart-container {
  transition: all 0.3s ease;
}

.chart-panel:hover .chart-container {
  transform: scale(1.02);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(102, 204, 255, 0.1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #66ccff, #33aaff);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #33aaff, #66ccff);
}
</style>
