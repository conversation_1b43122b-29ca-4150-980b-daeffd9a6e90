<template>
  <div class="bg-gray-50 text-dark min-h-screen overflow-x-hidden">
    <!-- 核心运营指标卡 -->
    <section class="w-full px-4 py-6">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 max-w-[1800px] mx-auto">
        <div class="bg-white rounded-xl p-5 card-shadow hover-lift">
          <div class="flex justify-between items-start">
            <div>
              <p class="text-gray-500 text-sm">当前总库存</p>
              <h3 class="text-2xl font-bold mt-1">1286.3吨</h3>
              <p class="text-success text-sm mt-2 flex items-center">
                <i class="fa fa-arrow-up mr-1"></i> 较上月增长 3.2%
              </p>
            </div>
            <div class="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center text-primary">
              <i class="fa fa-cubes text-xl"></i>
            </div>
          </div>
        </div>
     
        
        <div class="bg-white rounded-xl p-5 card-shadow hover-lift">
          <div class="flex justify-between items-start">
            <div>
              <p class="text-gray-500 text-sm">本月采购额</p>
              <h3 class="text-2xl font-bold mt-1">85万元</h3>
              <p class="text-danger text-sm mt-2 flex items-center">
                <i class="fa fa-arrow-down mr-1"></i> 较上月下降 2.1%
              </p>
            </div>
            <div class="w-12 h-12 rounded-full bg-warning/10 flex items-center justify-center text-warning">
              <i class="fa fa-truck text-xl"></i>
            </div>
          </div>
        </div>
        
        <div class="bg-white rounded-xl p-5 card-shadow hover-lift">
          <div class="flex justify-between items-start">
            <div>
              <p class="text-gray-500 text-sm">订单处理率</p>
              <h3 class="text-2xl font-bold mt-1">98.2%</h3>
              <p class="text-success text-sm mt-2 flex items-center">
                <i class="fa fa-arrow-up mr-1"></i> 较上月增长 1.5%
              </p>
            </div>
            <div class="w-12 h-12 rounded-full bg-success/10 flex items-center justify-center text-success">
              <i class="fa fa-check-circle text-xl"></i>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 主体内容区 -->
     <main class="w-full px-4 pb-10">
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 max-w-[1800px] mx-auto">
        <!-- 左侧列 -->
        <div class="space-y-4 md:space-y-6">
          <!-- 库存分析 -->
          <div class="bg-white rounded-xl p-4 md:p-5 card-shadow">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-base md:text-lg font-semibold">库存分析</h2>
              <div class="text-xs md:text-sm text-gray-500">
                <span>更新于 10分钟前</span>
              </div>
            </div>
            <div class="h-48 md:h-64 w-full">
              <canvas ref="inventoryChart"></canvas>
            </div>
          </div>
          
          <!-- 销售实况 -->
          <div class="bg-white rounded-xl p-4 md:p-5 card-shadow">
            <div class="flex justify-between items-center mb-4">
              <h2 class="text-base md:text-lg font-semibold">销售实况</h2>
              <button class="text-primary text-xs md:text-sm hover:underline">查看全部</button>
            </div>
            <div class="h-48 md:h-64 w-full">
              <canvas ref="salesChart"></canvas>
            </div>
            <div class="mt-4 grid grid-cols-5 gap-2 text-sm text-center text-gray-500">
              <div>单号</div>
              <div>名称</div>
              <div>数量</div>
              <div>金额</div>
              <div>销售员</div>
              <div v-for="sale in salesData" :key="sale.id">
                {{ sale.orderNumber }}
              </div>
              <div v-for="sale in salesData" :key="sale.id + 'name'">
                {{ sale.name }}
              </div>
              <div v-for="sale in salesData" :key="sale.id + 'qty'">
                {{ sale.quantity }}
              </div>
              <div v-for="sale in salesData" :key="sale.id + 'amt'">
                {{ sale.amount }}
              </div>
              <div v-for="sale in salesData" :key="sale.id + 'seller'">
                {{ sale.seller }}
              </div>
            </div>
          </div>
          
          <!-- 左侧第三行：采购单实况 -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <div class="flex justify-between items-center mb-5">
              <h2 class="text-lg font-semibold">采购单实况</h2>
              <button class="text-primary text-sm hover:underline">新增采购单</button>
            </div>
            <div class="overflow-x-auto">
              <table class="min-w-full">
                <thead>
                  <tr class="border-b border-gray-200">
                    <th class="py-3 text-left text-sm font-medium text-gray-500">单号</th>
                    <th class="py-3 text-left text-sm font-medium text-gray-500">名称</th>
                    <th class="py-3 text-left text-sm font-medium text-gray-500">数量</th>
                    <th class="py-3 text-left text-sm font-medium text-gray-500">金额</th>
                    <th class="py-3 text-left text-sm font-medium text-gray-500">状态</th>
                    <th class="py-3 text-left text-sm font-medium text-gray-500">操作</th>
                  </tr>
                </thead>
                <tbody>
                  <tr 
                    v-for="purchase in purchaseOrders" 
                    :key="purchase.id" 
                    class="border-b border-gray-100 hover:bg-gray-50"
                  >
                    <td class="py-3 text-sm">{{ purchase.orderNumber }}</td>
                    <td class="py-3 text-sm">{{ purchase.name }}</td>
                    <td class="py-3 text-sm">{{ purchase.quantity }}</td>
                    <td class="py-3 text-sm">{{ purchase.amount }}</td>
                    <td class="py-3 text-sm">
                      <span 
                        :class="purchase.statusClass"
                      >{{ purchase.status }}</span>
                    </td>
                    <td class="py-3 text-sm">
                      <button class="text-primary hover:underline">详情</button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        <!-- 中间列 -->
        <div class="space-y-6">
          <!-- 经营实况 -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <div class="flex justify-between items-center mb-5">
              <h2 class="text-lg font-semibold">经营实况</h2>
              <div class="flex space-x-2">
                <button 
                  class="px-3 py-1 text-sm bg-primary text-white rounded-full"
                  @click="setBusinessView('daily')"
                  :class="{ 'bg-primary text-white': businessView === 'daily' }"
                >日销售</button>
                <button 
                  class="px-3 py-1 text-sm bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200"
                  @click="setBusinessView('monthly')"
                  :class="{ 'bg-primary text-white': businessView === 'monthly' }"
                >月销售</button>
              </div>
            </div>
            <div class="h-80 w-full">
              <canvas ref="businessChart"></canvas>
            </div>
          </div>
          
          <!-- 品类TOPS -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <div class="flex justify-between items-center mb-5">
              <h2 class="text-lg font-semibold">品类TOPS</h2>
              <button class="text-primary text-sm hover:underline">更多品类</button>
            </div>
            <div class="h-72 w-full">
              <canvas ref="categoryChart"></canvas>
            </div>
          </div>
          
          <!-- 全局库存状态总览 -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <div class="flex justify-between items-center mb-5">
              <h2 class="text-lg font-semibold">库存区域分布</h2>
              <button class="text-primary text-sm hover:underline">查看详情</button>
            </div>
            <div class="grid grid-cols-3 gap-4">
              <div v-for="warehouse in warehouses" :key="warehouse.id" :class="warehouse.bgClass">
                <div class="flex justify-between items-start">
                  <div>
                    <h3 class="font-medium">{{ warehouse.name }}</h3>
                    <p class="text-sm text-gray-500 mt-1">主要存储：{{ warehouse.storage }}</p>
                    <p :class="warehouse.statusClass" class="text-sm mt-2">{{ warehouse.status }}</p>
                  </div>
                  <div :class="warehouse.iconBgClass">
                    <i :class="warehouse.iconClass"></i>
                  </div>
                </div>
                <div class="mt-3 h-2 bg-gray-100 rounded-full overflow-hidden">
                  <div :class="warehouse.progressClass" :style="{ width: warehouse.usage }"></div>
                </div>
                <p class="text-xs text-gray-500 mt-2 text-right">使用率 {{ warehouse.usage }}</p>
              </div>
            </div>
            
            <!-- 库存预警与建议 -->
            <div class="mt-6 p-4 bg-orange-50 rounded-lg border border-orange-100">
              <h3 class="font-medium text-orange-700 flex items-center">
                <i class="fa fa-lightbulb-o mr-2"></i>库存预警与建议
              </h3>
              <p class="text-sm text-gray-600 mt-2">B区龙虾库存预计将在5天后低于安全线，建议提前采购300吨补充库存。</p>
              <button class="mt-3 px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-full hover:bg-orange-200 transition-colors">
                生成采购建议
              </button>
            </div>
          </div>
        </div>
        
        <!-- 右侧列 -->
        <div class="space-y-6">
          <!-- 右侧第一行：订单满足率和采购到货率 -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <h2 class="text-lg font-semibold mb-5">订单与采购效率</h2>
            <div class="grid grid-cols-2 gap-4">
              <div class="flex flex-col items-center">
                <div class="h-40 w-40 relative">
                  <canvas ref="orderRateChart"></canvas>
                  <div class="absolute inset-0 flex items-center justify-center flex-col">
                    <span class="text-2xl font-bold text-primary">98%</span>
                    <span class="text-sm text-gray-500">订单满足率</span>
                  </div>
                </div>
              </div>
              <div class="flex flex-col items-center">
                <div class="h-40 w-40 relative">
                  <canvas ref="purchaseRateChart"></canvas>
                  <div class="absolute inset-0 flex items-center justify-center flex-col">
                    <span class="text-2xl font-bold text-secondary">92%</span>
                    <span class="text-sm text-gray-500">采购到货率</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧第二行：订单状态统计 -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <h2 class="text-lg font-semibold mb-5">订单状态统计</h2>
            <div class="grid grid-cols-3 gap-4">
              <div v-for="orderStatus in orderStatuses" :key="orderStatus.id" :class="orderStatus.bgClass">
                <div class="text-3xl font-bold" :class="orderStatus.numberClass">{{ orderStatus.count }}</div>
                <div class="text-sm text-gray-600">{{ orderStatus.label }}</div>
                <div :class="orderStatus.trendClass" class="mt-3 text-xs">
                  <i :class="orderStatus.trendIcon"></i> 较昨日 {{ orderStatus.trendValue }}
                </div>
              </div>
            </div>
          </div>
          
          <!-- 右侧第三行：区域销售排名 -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <div class="flex justify-between items-center mb-5">
              <h2 class="text-lg font-semibold">销售员业绩排名</h2>
              <div class="text-sm text-gray-500">本月</div>
            </div>
            <div class="h-64 w-full">
              <canvas ref="salesRankChart"></canvas>
            </div>
          </div>
          
          <!-- 任务协作看板 -->
          <div class="bg-white rounded-xl p-5 card-shadow">
            <div class="flex justify-between items-center mb-5">
              <h2 class="text-lg font-semibold">待处理任务</h2>
              <button class="px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90 transition-colors">
                <i class="fa fa-plus mr-1"></i>发布任务
              </button>
            </div>
            <div class="space-y-3">
              <div 
                v-for="task in tasks" 
                :key="task.id" 
                :class="task.bgClass"
              >
                <div class="flex justify-between">
                  <h3 class="font-medium">{{ task.title }}</h3>
                  <span :class="task.priorityClass">{{ task.priority }}</span>
                </div>
                <div class="mt-2 text-sm text-gray-600">
                  <p><span class="text-gray-500">负责人：</span>{{ task.responsible }}</p>
                  <p><span class="text-gray-500">截止时间：</span>{{ task.deadline }}</p>
                </div>
                <div class="mt-3 flex justify-end space-x-2">
                  <button class="px-3 py-1 text-sm border border-gray-200 rounded-full hover:bg-gray-50">
                    稍后处理
                  </button>
                  <button class="px-3 py-1 text-sm bg-primary text-white rounded-full hover:bg-primary/90">
                    立即处理
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted} from 'vue';
import Chart from 'chart.js/auto';

export default {
  setup() {
    // 时间更新相关
    const updateTime = ref('');

        // 页面加载时初始化
    onMounted(() => {
      // 更新时间
      updateTimeDisplay();
      setInterval(updateTimeDisplay, 1000);
      
      // 初始化所有图表
      setTimeout(() => {
        initInventoryChart();
        initSalesChart();
        initBusinessChart();
        initCategoryChart();
        initOrderRateChart();
        initPurchaseRateChart();
        initSalesRankChart();
      }, 100);
    });
    
    // 图表引用
    const inventoryChart = ref(null);
    const salesChart = ref(null);
    const businessChart = ref(null);
    const categoryChart = ref(null);
    const orderRateChart = ref(null);
    const purchaseRateChart = ref(null);
    const salesRankChart = ref(null);
    
    // 经营视图状态
    const businessView = ref('daily');
    
    // 销售数据
    const salesData = [
      { id: 1, orderNumber: 'X001', name: '虾仁', quantity: '100吨', amount: '10万', seller: '张三' },
      { id: 2, orderNumber: 'X002', name: '龙虾', quantity: '50吨', amount: '25万', seller: '李四' },
      { id: 3, orderNumber: 'X003', name: '虾面', quantity: '20万尾', amount: '8万', seller: '王五' }
    ];
    
    // 采购订单数据
    const purchaseOrders = [
      { 
        id: 1, 
        orderNumber: 'C001', 
        name: '饲料', 
        quantity: '100吨', 
        amount: '35万', 
        status: '已完成',
        statusClass: 'px-2 py-1 bg-success/10 text-success text-xs rounded-full'
      },
      { 
        id: 2, 
        orderNumber: 'C002', 
        name: '虾苗', 
        quantity: '50万尾', 
        amount: '20万', 
        status: '处理中',
        statusClass: 'px-2 py-1 bg-warning/10 text-warning text-xs rounded-full'
      },
      { 
        id: 3, 
        orderNumber: 'C003', 
        name: '龙虾苗', 
        quantity: '3000吨', 
        amount: '120万', 
        status: '待审核',
        statusClass: 'px-2 py-1 bg-info/10 text-info text-xs rounded-full'
      },
      { 
        id: 4, 
        orderNumber: 'C004', 
        name: '虾仁加工料', 
        quantity: '150吨', 
        amount: '45万', 
        status: '已取消',
        statusClass: 'px-2 py-1 bg-danger/10 text-danger text-xs rounded-full'
      }
    ];
    
    // 仓库数据
    const warehouses = [
      {
        id: 1,
        name: 'A区仓库',
        storage: '虾仁、虾面',
        status: '库存充足',
        usage: '65%',
        bgClass: 'bg-green-50 p-4 rounded-lg border border-green-100',
        statusClass: 'text-success',
        iconBgClass: 'w-8 h-8 rounded-full bg-success/20 flex items-center justify-center',
        iconClass: 'fa fa-check text-success',
        progressClass: 'h-full bg-success rounded-full'
      },
      {
        id: 2,
        name: 'B区仓库',
        storage: '龙虾',
        status: '需关注库存',
        usage: '88%',
        bgClass: 'bg-yellow-50 p-4 rounded-lg border border-yellow-100',
        statusClass: 'text-warning',
        iconBgClass: 'w-8 h-8 rounded-full bg-warning/20 flex items-center justify-center',
        iconClass: 'fa fa-exclamation text-warning',
        progressClass: 'h-full bg-warning rounded-full'
      },
      {
        id: 3,
        name: 'C区仓库',
        storage: '饲料',
        status: '正常',
        usage: '42%',
        bgClass: 'bg-blue-50 p-4 rounded-lg border border-blue-100',
        statusClass: 'text-primary',
        iconBgClass: 'w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center',
        iconClass: 'fa fa-info text-primary',
        progressClass: 'h-full bg-primary rounded-full'
      }
    ];
    
    // 订单状态数据
    const orderStatuses = [
      {
        id: 1,
        count: 12,
        label: '未处理订单',
        bgClass: 'bg-red-50 p-4 rounded-lg border border-red-100 text-center',
        numberClass: 'text-danger',
        trendClass: 'text-red-500',
        trendIcon: 'fa fa-arrow-up mr-1',
        trendValue: '+2'
      },
      {
        id: 2,
        count: 28,
        label: '已发送订单',
        bgClass: 'bg-yellow-50 p-4 rounded-lg border border-yellow-100 text-center',
        numberClass: 'text-warning',
        trendClass: 'text-green-500',
        trendIcon: 'fa fa-arrow-down mr-1',
        trendValue: '-5'
      },
      {
        id: 3,
        count: 156,
        label: '已完成订单',
        bgClass: 'bg-green-50 p-4 rounded-lg border border-green-100 text-center',
        numberClass: 'text-success',
        trendClass: 'text-green-500',
        trendIcon: 'fa fa-arrow-up mr-1',
        trendValue: '+12'
      }
    ];
    
    // 任务数据
    const tasks = [
      {
        id: 1,
        title: '审核C003龙虾苗采购单',
        priority: '紧急',
        responsible: '采购部 - 赵经理',
        deadline: '今日 18:00前',
        bgClass: 'p-3 border border-red-100 bg-red-50 rounded-lg',
        priorityClass: 'px-2 py-0.5 bg-red-100 text-red-700 text-xs rounded-full'
      },
      {
        id: 2,
        title: 'B区龙虾库存盘点',
        priority: '中等',
        responsible: '库存部 - 孙主管',
        deadline: '明日 12:00前',
        bgClass: 'p-3 border border-yellow-100 bg-yellow-50 rounded-lg',
        priorityClass: 'px-2 py-0.5 bg-yellow-100 text-yellow-700 text-xs rounded-full'
      },
      {
        id: 3,
        title: '8月份销售报表生成',
        priority: '常规',
        responsible: '销售部 - 李助理',
        deadline: '08/25 前',
        bgClass: 'p-3 border border-blue-100 bg-blue-50 rounded-lg',
        priorityClass: 'px-2 py-0.5 bg-blue-100 text-blue-700 text-xs rounded-full'
      }
    ];
    
    // 更新时间函数
    const updateTimeDisplay = () => {
      const now = new Date();
      const month = now.getMonth() + 1;
      const day = now.getDate();
      const hours = now.getHours().toString().padStart(2, '0');
      const minutes = now.getMinutes().toString().padStart(2, '0');
      const seconds = now.getSeconds().toString().padStart(2, '0');
      updateTime.value = `${month}/${day} ${hours}:${minutes}:${seconds}`;
    };
    
    // 设置经营视图
    const setBusinessView = (view) => {
      businessView.value = view;
      initBusinessChart(); // 重新初始化图表
    };
    
    // 初始化库存分析图表
    const initInventoryChart = () => {
      if (inventoryChart.value) {
        new Chart(inventoryChart.value.getContext('2d'), {
          type: 'bar',
          data: {
            labels: ['虾面', '龙虾', '虾仁', '饲料'],
            datasets: [{
              label: '库存量',
              data: [120, 8500, 300, 100],
              backgroundColor: [
                'rgba(22, 93, 255, 0.7)',
                'rgba(15, 198, 194, 0.7)',
                'rgba(255, 125, 0, 0.7)',
                'rgba(0, 180, 42, 0.7)'
              ],
              borderWidth: 0,
              borderRadius: 4
            }]
          },
          options: {
            responsive: true,  // 响应式开启
            maintainAspectRatio: false,  // 关闭强制宽高比
            devicePixelRatio: 2, // 或者 window.devicePixelRatio || 2
            indexAxis: 'y',
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                callbacks: {
                  label: function(context) {
                    const labels = ['万尾', '吨', '吨', '吨'];
                    return `${context.raw}${labels[context.dataIndex]}`;
                  }
                }
              }
            },
            scales: {
              x: {
                beginAtZero: true,
                grid: {
                  display: false
                }
              },
              y: {
                grid: {
                  display: false
                }
              }
            }
          }
        });
      }
    };
    
    // 初始化销售实况图表
    const initSalesChart = () => {
      if (salesChart.value) {
        new Chart(salesChart.value.getContext('2d'), {
          type: 'line',
          data: {
            labels: ['X001', 'X002', 'X003', 'X004', 'X005', 'X006'],
            datasets: [{
              label: '销售额(万元)',
              data: [10, 25, 8, 15, 30, 12],
              borderColor: 'rgba(22, 93, 255, 1)',
              backgroundColor: 'rgba(22, 93, 255, 0.1)',
              tension: 0.4,
              fill: true,
              pointBackgroundColor: 'rgba(22, 93, 255, 1)',
              pointRadius: 4,
              pointHoverRadius: 6
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            devicePixelRatio: 2, // 或者 window.devicePixelRatio || 2
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  borderDash: [2, 4],
                  color: 'rgba(0, 0, 0, 0.05)'
                }
              },
              x: {
                grid: {
                  display: false
                }
              }
            }
          }
        });
      }
    };
    
    // 初始化经营实况图表
    const initBusinessChart = () => {
      if (businessChart.value) {
        const labels = ['1日', '5日', '10日', '15日', '20日', '25日', '30日'];
        let dailyData, monthlyData;
        
        if (businessView.value === 'daily') {
          dailyData = [5.2, 4.8, 6.5, 7.2, 5.9, 8.1, 7.5];
          monthlyData = [5.2, 25.6, 68.3, 125.8, 186.5, 258.7, 326.9];
        } else {
          dailyData = [32, 28, 35, 42, 38, 45, 40];
          monthlyData = [32, 95, 186, 298, 412, 587, 726];
        }
        
        new Chart(businessChart.value.getContext('2d'), {
          type: 'line',
          data: {
            labels: labels,
            datasets: [{
              label: '日销售额(万元)',
              data: dailyData,
              borderColor: 'rgba(22, 93, 255, 1)',
              backgroundColor: 'rgba(22, 93, 255, 0.1)',
              tension: 0.4,
              fill: true,
              yAxisID: 'y'
            }, {
              label: '月累计销售额(万元)',
              data: monthlyData,
              borderColor: 'rgba(15, 198, 194, 1)',
              backgroundColor: 'transparent',
              tension: 0.4,
              borderDash: [5, 5],
              yAxisID: 'y1'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            devicePixelRatio: 2, // 或者 window.devicePixelRatio || 2
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  boxWidth: 12,
                  usePointStyle: true,
                  pointStyle: 'circle'
                }
              }
            },
            scales: {
              y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                  display: true,
                  text: '日销售额(万元)'
                },
                grid: {
                  borderDash: [2, 4],
                  color: 'rgba(0, 0, 0, 0.05)'
                }
              },
              y1: {
                type: 'linear',
                display: true,
                position: 'right',
                title: {
                  display: true,
                  text: '月累计销售额(万元)'
                },
                grid: {
                  drawOnChartArea: false
                }
              },
              x: {
                grid: {
                  display: false
                }
              }
            }
          }
        });
      }
    };
    
    // 初始化品类TOPS图表
    const initCategoryChart = () => {
      if (categoryChart.value) {
        new Chart(categoryChart.value.getContext('2d'), {
          type: 'bar',
          data: {
            labels: ['No1 虾仁', 'No2 龙虾', 'No3 虾面', 'No4 饲料', 'No5 虾酱'],
            datasets: [{
              label: '销售额(万元)',
              data: [100, 80, 65, 35, 20],
              backgroundColor: 'rgba(22, 93, 255, 0.7)',
              borderWidth: 0,
              borderRadius: 4,
              yAxisID: 'y'
            }, {
              label: '占比(%)',
              data: [45, 36, 29, 16, 9],
              backgroundColor: 'rgba(15, 198, 194, 0.7)',
              borderWidth: 0,
              borderRadius: 4,
              yAxisID: 'y1'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            devicePixelRatio: 2, // 或者 window.devicePixelRatio || 2
            indexAxis: 'y',
            plugins: {
              legend: {
                position: 'top',
                labels: {
                  boxWidth: 12,
                  usePointStyle: true,
                  pointStyle: 'circle'
                }
              }
            },
            scales: {
              x: {
                type: 'linear',
                display: true,
                position: 'bottom',
                title: {
                  display: true,
                  text: '销售额(万元)'
                },
                grid: {
                  display: false
                },
                yAxisID: 'y'
              },
              x1: {
                type: 'linear',
                display: true,
                position: 'top',
                title: {
                  display: true,
                  text: '占比(%)'
                },
                grid: {
                  display: false
                },
                yAxisID: 'y1'
              },
              y: {
                grid: {
                  display: false
                }
              }
            }
          }
        });
      }
    };
    
    // 初始化订单满足率图表
    const initOrderRateChart = () => {
      if (orderRateChart.value) {
        new Chart(orderRateChart.value.getContext('2d'), {
          type: 'doughnut',
          data: {
            datasets: [{
              data: [98, 2],
              backgroundColor: [
                'rgba(22, 93, 255, 0.8)',
                'rgba(242, 243, 245, 1)'
              ],
              borderWidth: 0,
              cutout: '80%'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            devicePixelRatio: 2, // 或者 window.devicePixelRatio || 2
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                enabled: false
              }
            },
            rotation: -90,
            circumference: 180
          }
        });
      }
    };
    
    // 初始化采购到货率图表
    const initPurchaseRateChart = () => {
      if (purchaseRateChart.value) {
        new Chart(purchaseRateChart.value.getContext('2d'), {
          type: 'doughnut',
          data: {
            datasets: [{
              data: [92, 8],
              backgroundColor: [
                'rgba(15, 198, 194, 0.8)',
                'rgba(242, 243, 245, 1)'
              ],
              borderWidth: 0,
              cutout: '80%'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              },
              tooltip: {
                enabled: false
              }
            },
            rotation: -90,
            circumference: 180
          }
        });
      }
    };
    
    // 初始化销售排名图表
    const initSalesRankChart = () => {
      if (salesRankChart.value) {
        new Chart(salesRankChart.value.getContext('2d'), {
          type: 'bar',
          data: {
            labels: ['张三', '李四', '王五', '赵六', '钱七', '孙八'],
            datasets: [{
              label: '销售额(万元)',
              data: [45, 38, 32, 28, 22, 18],
              backgroundColor: [
                'rgba(22, 93, 255, 0.8)',
                'rgba(22, 93, 255, 0.7)',
                'rgba(22, 93, 255, 0.6)',
                'rgba(22, 93, 255, 0.5)',
                'rgba(22, 93, 255, 0.4)',
                'rgba(22, 93, 255, 0.3)'
              ],
              borderWidth: 0,
              borderRadius: 4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
              legend: {
                display: false
              }
            },
            scales: {
              y: {
                beginAtZero: true,
                grid: {
                  borderDash: [2, 4],
                  color: 'rgba(0, 0, 0, 0.05)'
                }
              },
              x: {
                grid: {
                  display: false
                }
              }
            }
          }
        });
      }
    };
    
    // 页面加载时初始化
    onMounted(() => {
      // 更新时间
      updateTimeDisplay();
      setInterval(updateTimeDisplay, 1000);
      
      // 初始化所有图表
      initInventoryChart();
      initSalesChart();
      initBusinessChart();
      initCategoryChart();
      initOrderRateChart();
      initPurchaseRateChart();
      initSalesRankChart();
    });
    
    return {
      updateTime,
      inventoryChart,
      salesChart,
      businessChart,
      categoryChart,
      orderRateChart,
      purchaseRateChart,
      salesRankChart,
      businessView,
      salesData,
      purchaseOrders,
      warehouses,
      orderStatuses,
      tasks,
      setBusinessView
    };
  }
};
</script>
<style scoped>
/* 引入外部资源 */
@import url('https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css');

@layer utilities {
  .content-auto {
    content-visibility: auto;
  }
  .card-shadow {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  }
  .hover-lift {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }
  .hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  }
  .gradient-bg {
    background: linear-gradient(135deg, #165DFF 0%, #0FC6C2 100%);
  }
}



/* 添加滚动支持和响应式调整 */
@media (max-width: 1024px) {
  .grid-cols-3 {
    grid-template-columns: 1fr;
  }
}

/* 确保内容可以滚动 */
.min-h-screen {
  min-height: 100vh;
}

/* 优化移动端显示 */
@media (max-width: 768px) {
  .px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .gap-4 {
    gap: 1rem;
  }
  
  .text-xl {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }
}

/* 防止水平滚动条出现 */
.overflow-x-hidden {
  overflow-x: hidden;
}

/* 确保图表容器正确显示 */
canvas {
  display: block;
  max-width: 100%;
}
</style>