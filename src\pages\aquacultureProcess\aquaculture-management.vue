<template>
  <div class="bg-gray-50 text-dark min-h-screen flex flex-col">

    <!-- 主内容区 -->
    <main class="flex-1 flex overflow-hidden">
      <!-- 左侧导航 -->
      <aside class="w-64 bg-white border-r border-gray-200 flex-shrink-0 hidden md:block overflow-y-auto" :class="{ 'fixed inset-y-0 left-0 z-50 fade-in': mobileMenuOpen }">
        <div class="p-4">
          <div class="mb-6">
            <h2 class="text-xs font-semibold text-gray-500 uppercase tracking-wider">楼栋选择</h2>
            <nav class="mt-3 space-y-1">
              <div 
                v-for="building in buildings" 
                :key="building.id"
                class="space-y-1"
                :id="`building-${building.id}-desktop`"
              >
                <!-- 楼栋标题 -->
                <div class="flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md">
                  <div 
                    class="flex items-center cursor-pointer hover:bg-gray-100 rounded-md px-2 py-1 flex-1"
                    @click="selectBuilding(building.id)"
                  >
                    <i class="fa fa-building mr-3" :class="selectedBuilding === building.id ? '' : 'text-gray-400'"></i>
                    <span :class="selectedBuilding === building.id ? 'text-primary' : 'text-gray-600'">{{ building.name }}</span>
                  </div>
                  <div 
                    class="cursor-pointer hover:bg-gray-100 rounded-md p-1"
                    @click="toggleBuilding(building.id)"
                  >
                    <i 
                      class="fa fa-chevron-down text-xs transition-transform duration-200"
                      :class="expandedBuildings.includes(building.id) ? 'rotate-180' : ''"
                    ></i>
                  </div>
                </div>
                
                <!-- 楼层列表 -->
                <div 
                  v-if="expandedBuildings.includes(building.id)"
                  class="ml-6 space-y-1"
                >
                  <div 
                    v-for="floor in building.floors" 
                    :key="floor.id"
                    class="group flex items-center px-3 py-2 text-sm font-medium rounded-md cursor-pointer"
                    :class="selectedFloor === floor.id ? 'bg-primary/10 text-primary' : 'text-gray-500 hover:bg-gray-100'"
                    @click="selectFloor(building.id, floor.id)"
                  >
                    <i class="fa fa-layer-group mr-3" :class="selectedFloor === floor.id ? '' : 'text-gray-400'"></i>
                    {{ floor.name }}
                  </div>
                </div>
              </div>
            </nav>
          </div>
          

        </div>
      </aside>

      <!-- 移动端菜单按钮 -->
      <button 
        id="mobile-menu-button" 
        class="md:hidden fixed bottom-6 right-6 z-40 w-12 h-12 rounded-full gradient-bg text-white shadow-lg flex items-center justify-center"
        @click="toggleMobileMenu"
      >
        <i class="fa fa-bars"></i>
      </button>

      <!-- 移动端楼层选择 -->
      <div v-if="mobileMenuOpen" class="md:hidden fixed inset-0 z-50 bg-black bg-opacity-50">
        <div class="fixed inset-y-0 left-0 w-64 bg-white shadow-xl overflow-y-auto">
          <div class="p-4">
            <div class="flex justify-between items-center mb-6">
              <h2 class="text-lg font-semibold text-gray-900">楼栋选择</h2>
              <button @click="toggleMobileMenu" class="text-gray-400 hover:text-gray-600">
                <i class="fa fa-times text-xl"></i>
              </button>
            </div>
            <nav class="space-y-1">
              <div 
                v-for="building in buildings" 
                :key="building.id"
                class="space-y-1"
                :id="`building-${building.id}-mobile`"
              >
                <!-- 楼栋标题 -->
                <div class="flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md">
                  <div 
                    class="flex items-center cursor-pointer hover:bg-gray-100 rounded-md px-2 py-1 flex-1"
                    @click="selectBuilding(building.id)"
                  >
                    <i class="fa fa-building mr-3" :class="selectedBuilding === building.id ? '' : 'text-gray-400'"></i>
                    <span :class="selectedBuilding === building.id ? 'text-primary' : 'text-gray-600'">{{ building.name }}</span>
                  </div>
                  <div 
                    class="cursor-pointer hover:bg-gray-100 rounded-md p-1"
                    @click="toggleBuilding(building.id)"
                  >
                    <i 
                      class="fa fa-chevron-down text-xs transition-transform duration-200"
                      :class="expandedBuildings.includes(building.id) ? 'rotate-180' : ''"
                    ></i>
                  </div>
                </div>
                
                <!-- 楼层列表 -->
                <div 
                  v-if="expandedBuildings.includes(building.id)"
                  class="ml-6 space-y-1"
                >
                  <div 
                    v-for="floor in building.floors" 
                    :key="floor.id"
                    class="group flex items-center px-3 py-2 text-sm font-medium rounded-md cursor-pointer"
                    :class="selectedFloor === floor.id ? 'bg-primary/10 text-primary' : 'text-gray-500 hover:bg-gray-100'"
                    @click="selectFloor(building.id, floor.id)"
                  >
                    <i class="fa fa-layer-group mr-3" :class="selectedFloor === floor.id ? '' : 'text-gray-400'"></i>
                    {{ floor.name }}
                  </div>
                </div>
              </div>
            </nav>
          </div>
        </div>
      </div>

       <!-- 主内容滚动区域 -->
       <div class="flex-1 overflow-y-auto p-4 md:p-6 bg-gray-50">
         <!-- 页面切换逻辑 -->
         <div v-if="currentPage === 'management'">
           <!-- 主要内容区域 - 2栏布局 -->
           <div class="grid grid-cols-1 lg:grid-cols-10 gap-6">
             <!-- 第1栏 - 占比6 -->
             <div class="lg:col-span-6 space-y-6">
               <!-- 上半部分 - 空白区域 -->
               <div class="bg-white rounded-xl p-6 card-shadow">
                <div class="h-104 overflow-hidden rounded-lg">
                  <img :src="buildingImg" alt="楼栋示意" class="w-full h-full " />
                </div>
               </div>
               
               <!-- 下半部分 - 设备类型状态 -->
               <div class="bg-white rounded-xl p-6 card-shadow">
                 <h3 class="text-lg font-semibold text-gray-900 mb-4">设备类型状态</h3>
                 <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                   <div 
                     v-for="deviceType in currentFloorData.deviceTypes" 
                     :key="deviceType.name"
                     class="bg-gray-50 rounded-lg p-4"
                   >
                     <div class="text-center">
                       <div class="flex justify-around items-center mb-3">
                         <div class="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center">
                           <i :class="getIconClass(deviceType.icon)" class="text-blue-600 text-xl"></i>
                         </div>
                          <h4 class="text-lg font-semibold text-gray-900 mr-2" style="margin-left:10px">{{ deviceType.name }}</h4>
                          <div class="flex items-center justify-center mb-2">
                            <span class="bg-green-100 text-green-800 text-base px-2 py-1 rounded-full">{{ deviceType.total }}台</span>
                          </div>
                       </div>
                      
                       <div class="space-y-1 text-sm">
                        <div class="flex justify-between gap-2">
                          <div class="flex-1 flex flex-col items-center">
                            <span class="text-gray-500 text-lg">正常</span>
                            <span class="text-green-600 font-medium text-2xl">{{ deviceType.normal }}</span>
                          </div>
                          <div class="flex-1 flex flex-col items-center">
                            <span class="text-gray-500 text-lg">警告</span>
                            <span class="text-yellow-600 font-medium text-2xl">{{ deviceType.warning }}</span>
                          </div>
                          <div class="flex-1 flex flex-col items-center">
                            <span class="text-gray-500 text-lg">异常</span>
                            <span class="text-red-600 font-medium text-2xl">{{ deviceType.abnormal }}</span>
                          </div>
                        </div>
                       </div>
                     </div>
                   </div>
                 </div>
               </div>
             </div>
 
             <!-- 第2栏 - 占比4 -->
             <div class="lg:col-span-4 space-y-6">
               <!-- 上部分 - 生长阶段分析 -->
               <div class="bg-white rounded-xl p-6 card-shadow">
                 <h3 class="text-lg font-semibold text-gray-900 mb-4">生长阶段分析</h3>
                 <div class="h-48">
                   <canvas ref="growthStageChart"></canvas>
                 </div>
               </div>
               
               <!-- 中部分 - 水质监控 -->
               <div class="bg-white rounded-xl p-6 card-shadow">
                 <h3 class="text-lg font-semibold text-gray-900 mb-4">水质监控</h3>
                 <div class="h-48">
                   <canvas ref="waterQualityChart"></canvas>
                 </div>
                
               </div>
               
               <!-- 下部分 - 设备状态和预警信息 -->
               <div class="bg-white rounded-xl p-6 card-shadow">
                 <h3 class="text-lg font-semibold text-gray-900 mb-4">设备状态和预警信息</h3>
                 <div class="space-y-3">
                   <div 
                     v-for="device in devices" 
                     :key="device.name"
                     class="flex items-center p-3 bg-gray-50 rounded-lg"
                   >
                     <div class="w-10 h-10 rounded-full flex items-center justify-center mr-3"
                          :class="device.bgColor">
                       <i :class="device.icon + ' ' + device.textColor"></i>
                     </div>
                     <div class="flex-1">
                       <h4 class="text-sm font-medium text-gray-900">{{ device.name }}</h4>
                       <p class="text-xs text-gray-500">{{ device.pool }}</p>
                     </div>
                     <span class="text-xs px-2 py-1 rounded-full"
                           :class="device.statusBg + ' ' + device.statusText">
                       {{ device.status }}
                     </span>
                   </div>
                 </div>
               </div>
             </div>
           </div>
         </div>
         
         <!-- 楼层信息页面 -->
         <div v-else-if="currentPage === 'floorInfo'">
           <FloorInformation :buildingId="selectedBuilding" :floorId="selectedFloor" />
         </div>
       </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, reactive, computed, watch, nextTick } from 'vue';
import Chart from 'chart.js/auto';
import FloorInformation from './floorInformation.vue';

export default {
  components: {
    FloorInformation
  },
  setup() {
    // 响应式数据
    const mobileMenuOpen = ref(false);
    const lastUpdateTime = ref('');
    const growthStageChart = ref(null);
    const waterQualityChart = ref(null);
    const growthStageChartInstance = ref(null);
    const waterQualityChartInstance = ref(null);
    const selectedFloor = ref(1);
    const currentPage = ref('management');
    
    // 楼层数据
    const floors = reactive([
      { id: 1, name: '1#楼层' },
      { id: 2, name: '2#楼层' },
      { id: 3, name: '3#楼层' },
      { id: 4, name: '4#楼层' },
      { id: 5, name: '5#楼层' },
      { id: 6, name: '6#楼层' },
      { id: 7, name: '7#楼层' },
      { id: 8, name: '8#楼层' },
      { id: 9, name: '9#楼层' },
      { id: 10, name: '10#楼层' }
    ]);
    
    // 楼栋数据
    const buildings = reactive([
      {
        id: 1,
        name: '第001号楼',
        floors: [
          { id: 1, name: '第01层' },
          { id: 2, name: '第02层' },
          { id: 3, name: '第03层' },
          { id: 4, name: '第04层' },
          { id: 5, name: '第05层' },
          { id: 6, name: '第06层' }
        ]
      },
      {
        id: 2,
        name: '第002号楼',
        floors: [
          { id: 7, name: '第01层' },
          { id: 8, name: '第02层' },
          { id: 9, name: '第03层' },
          { id: 10, name: '第04层' },
          { id: 11, name: '第05层' },
          { id: 12, name: '第06层' }
        ]
      },
      {
        id: 3,
        name: '第003号楼',
        floors: [
          { id: 13, name: '第01层' },
          { id: 14, name: '第02层' },
          { id: 15, name: '第03层' },
          { id: 16, name: '第04层' },
          { id: 17, name: '第05层' },
          { id: 18, name: '第06层' }
        ]
      },
      {
        id: 4,
        name: '第004号楼',
        floors: [
          { id: 19, name: '第01层' },
          { id: 20, name: '第02层' },
          { id: 21, name: '第03层' },
          { id: 22, name: '第04层' },
          { id: 23, name: '第05层' },
          { id: 24, name: '第06层' }
        ]
      },
      {
        id: 5,
        name: '第005号楼',
        floors: [
          { id: 25, name: '第01层' },
          { id: 26, name: '第02层' },
          { id: 27, name: '第03层' },
          { id: 28, name: '第04层' },
          { id: 29, name: '第05层' },
          { id: 30, name: '第06层' }
        ]
      },
      {
        id: 6,
        name: '第006号楼',
        floors: [
          { id: 31, name: '第01层' },
          { id: 32, name: '第02层' },
          { id: 33, name: '第03层' },
          { id: 34, name: '第04层' },
          { id: 35, name: '第05层' },
          { id: 36, name: '第06层' }
        ]
      },
      {
        id: 7,
        name: '第007号楼',
        floors: [
          { id: 37, name: '第01层' },
          { id: 38, name: '第02层' },
          { id: 39, name: '第03层' },
          { id: 40, name: '第04层' },
          { id: 41, name: '第05层' },
          { id: 42, name: '第06层' }
        ]
      },
      {
        id: 8,
        name: '第008号楼',
        floors: [
          { id: 43, name: '第01层' },
          { id: 44, name: '第02层' },
          { id: 45, name: '第03层' },
          { id: 46, name: '第04层' },
          { id: 47, name: '第05层' },
          { id: 48, name: '第06层' }
        ]
      },
      {
        id: 9,
        name: '第009号楼',
        floors: [
          { id: 49, name: '第01层' },
          { id: 50, name: '第02层' },
          { id: 51, name: '第03层' },
          { id: 52, name: '第04层' },
          { id: 53, name: '第05层' },
          { id: 54, name: '第06层' }
        ]
      },
      {
        id: 10,
        name: '第010号楼',
        floors: [
          { id: 55, name: '第01层' },
          { id: 56, name: '第02层' },
          { id: 57, name: '第03层' },
          { id: 58, name: '第04层' },
          { id: 59, name: '第05层' },
          { id: 60, name: '第06层' }
        ]
      },
      {
        id: 11,
        name: '第011号楼',
        floors: [
          { id: 61, name: '第01层' },
          { id: 62, name: '第02层' },
          { id: 63, name: '第03层' },
          { id: 64, name: '第04层' },
          { id: 65, name: '第05层' },
          { id: 66, name: '第06层' }
        ]
      },
      {
        id: 12,
        name: '第012号楼',
        floors: [
          { id: 67, name: '第01层' },
          { id: 68, name: '第02层' },
          { id: 69, name: '第03层' },
          { id: 70, name: '第04层' },
          { id: 71, name: '第05层' },
          { id: 72, name: '第06层' }
        ]
      },
      {
        id: 13,
        name: '第013号楼',
        floors: [
          { id: 73, name: '第01层' },
          { id: 74, name: '第02层' },
          { id: 75, name: '第03层' },
          { id: 76, name: '第04层' },
          { id: 77, name: '第05层' },
          { id: 78, name: '第06层' }
        ]
      },
      {
        id: 14,
        name: '第014号楼',
        floors: [
          { id: 79, name: '第01层' },
          { id: 80, name: '第02层' },
          { id: 81, name: '第03层' },
          { id: 82, name: '第04层' },
          { id: 83, name: '第05层' },
          { id: 84, name: '第06层' }
        ]
      },
      {
        id: 15,
        name: '第015号楼',
        floors: [
          { id: 85, name: '第01层' },
          { id: 86, name: '第02层' },
          { id: 87, name: '第03层' },
          { id: 88, name: '第04层' },
          { id: 89, name: '第05层' },
          { id: 90, name: '第06层' }
        ]
      },
      {
        id: 16,
        name: '第016号楼',
        floors: [
          { id: 91, name: '第01层' },
          { id: 92, name: '第02层' },
          { id: 93, name: '第03层' },
          { id: 94, name: '第04层' },
          { id: 95, name: '第05层' },
          { id: 96, name: '第06层' }
        ]
      },
      {
        id: 17,
        name: '第017号楼',
        floors: [
          { id: 97, name: '第01层' },
          { id: 98, name: '第02层' },
          { id: 99, name: '第03层' },
          { id: 100, name: '第04层' },
          { id: 101, name: '第05层' },
          { id: 102, name: '第06层' }
        ]
      },
      {
        id: 18,
        name: '第018号楼',
        floors: [
          { id: 103, name: '第01层' },
          { id: 104, name: '第02层' },
          { id: 105, name: '第03层' },
          { id: 106, name: '第04层' },
          { id: 107, name: '第05层' },
          { id: 108, name: '第06层' }
        ]
      },
      {
        id: 19,
        name: '第019号楼',
        floors: [
          { id: 109, name: '第01层' },
          { id: 110, name: '第02层' },
          { id: 111, name: '第03层' },
          { id: 112, name: '第04层' },
          { id: 113, name: '第05层' },
          { id: 114, name: '第06层' }
        ]
      },
      {
        id: 20,
        name: '第020号楼',
        floors: [
          { id: 115, name: '第01层' },
          { id: 116, name: '第02层' },
          { id: 117, name: '第03层' },
          { id: 118, name: '第04层' },
          { id: 119, name: '第05层' },
          { id: 120, name: '第06层' }
        ]
      }
    ]);

    const expandedBuildings = ref([1]); // 默认展开第一个楼栋
    const selectedBuilding = ref(1); // 默认选中第一个楼栋

    // 楼层设备数据
    const floorData = reactive({
      1: {
        totalDevices: 186,
        deviceIncrease: 5,
        onlineDevices: 179,
        onlineRate: 96.2,
        offlineDevices: 7,
        offlineRate: 3.8,
        warningDevices: 4,
        abnormalDevices: 2,
        todayAlarms: 12,
        processedAlarms: 8,
        deviceTypes: [
          { name: '投喂机', total: 42, normal: 40, warning: 1, abnormal: 1, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 26, normal: 24, warning: 1, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 20, normal: 19, warning: 0, abnormal: 1, icon: 'fa fa-leaf' },
          { name: '水泵', total: 12, normal: 11, warning: 0, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 8, normal: 7, warning: 1, abnormal: 0, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 6, normal: 5, warning: 1, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 5, normal: 4, warning: 1, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 4, normal: 4, warning: 0, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      2: {
        totalDevices: 165,
        deviceIncrease: 3,
        onlineDevices: 158,
        onlineRate: 95.8,
        offlineDevices: 7,
        offlineRate: 4.2,
        warningDevices: 6,
        abnormalDevices: 3,
        todayAlarms: 15,
        processedAlarms: 10,
        deviceTypes: [
          { name: '投喂机', total: 38, normal: 35, warning: 2, abnormal: 1, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 24, normal: 22, warning: 1, abnormal: 1, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 18, normal: 17, warning: 1, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 10, normal: 9, warning: 0, abnormal: 1, icon: 'fa fa-tint' },
          { name: '水阀', total: 5, normal: 4, warning: 1, abnormal: 0, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 4, normal: 3, warning: 1, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      3: {
        totalDevices: 142,
        deviceIncrease: 2,
        onlineDevices: 135,
        onlineRate: 95.1,
        offlineDevices: 7,
        offlineRate: 4.9,
        warningDevices: 5,
        abnormalDevices: 1,
        todayAlarms: 8,
        processedAlarms: 6,
        deviceTypes: [
          { name: '投喂机', total: 32, normal: 30, warning: 1, abnormal: 1, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 20, normal: 19, warning: 1, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 16, normal: 15, warning: 1, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 8, normal: 7, warning: 1, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 6, normal: 6, warning: 0, abnormal: 0, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 4, normal: 4, warning: 0, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      4: {
        totalDevices: 128,
        deviceIncrease: 1,
        onlineDevices: 122,
        onlineRate: 95.3,
        offlineDevices: 6,
        offlineRate: 4.7,
        warningDevices: 3,
        abnormalDevices: 2,
        todayAlarms: 6,
        processedAlarms: 4,
        deviceTypes: [
          { name: '投喂机', total: 28, normal: 26, warning: 1, abnormal: 1, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 18, normal: 17, warning: 1, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 14, normal: 13, warning: 0, abnormal: 1, icon: 'fa fa-leaf' },
          { name: '水泵', total: 7, normal: 6, warning: 1, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 6, normal: 5, warning: 0, abnormal: 1, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 4, normal: 3, warning: 1, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      5: {
        totalDevices: 115,
        deviceIncrease: 0,
        onlineDevices: 110,
        onlineRate: 95.7,
        offlineDevices: 5,
        offlineRate: 4.3,
        warningDevices: 4,
        abnormalDevices: 1,
        todayAlarms: 5,
        processedAlarms: 3,
        deviceTypes: [
          { name: '投喂机', total: 25, normal: 23, warning: 1, abnormal: 1, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 16, normal: 15, warning: 1, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 12, normal: 11, warning: 1, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 6, normal: 5, warning: 1, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 6, normal: 5, warning: 0, abnormal: 1, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 4, normal: 3, warning: 1, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      6: {
        totalDevices: 98,
        deviceIncrease: -1,
        onlineDevices: 94,
        onlineRate: 95.9,
        offlineDevices: 4,
        offlineRate: 4.1,
        warningDevices: 2,
        abnormalDevices: 1,
        todayAlarms: 3,
        processedAlarms: 2,
        deviceTypes: [
          { name: '投喂机', total: 22, normal: 20, warning: 1, abnormal: 1, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 14, normal: 13, warning: 1, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 10, normal: 9, warning: 1, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 5, normal: 4, warning: 1, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 5, normal: 4, warning: 0, abnormal: 1, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 4, normal: 3, warning: 1, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      7: {
        totalDevices: 85,
        deviceIncrease: 0,
        onlineDevices: 82,
        onlineRate: 96.5,
        offlineDevices: 3,
        offlineRate: 3.5,
        warningDevices: 1,
        abnormalDevices: 0,
        todayAlarms: 2,
        processedAlarms: 1,
        deviceTypes: [
          { name: '投喂机', total: 18, normal: 17, warning: 1, abnormal: 0, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 12, normal: 12, warning: 0, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 8, normal: 8, warning: 0, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 4, normal: 4, warning: 0, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 5, normal: 4, warning: 1, abnormal: 0, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 4, normal: 4, warning: 0, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      8: {
        totalDevices: 72,
        deviceIncrease: 1,
        onlineDevices: 69,
        onlineRate: 95.8,
        offlineDevices: 3,
        offlineRate: 4.2,
        warningDevices: 2,
        abnormalDevices: 1,
        todayAlarms: 4,
        processedAlarms: 2,
        deviceTypes: [
          { name: '投喂机', total: 15, normal: 14, warning: 1, abnormal: 0, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 10, normal: 9, warning: 1, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 7, normal: 6, warning: 1, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 4, normal: 3, warning: 1, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 4, normal: 3, warning: 0, abnormal: 1, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 3, normal: 2, warning: 1, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      9: {
        totalDevices: 58,
        deviceIncrease: 0,
        onlineDevices: 56,
        onlineRate: 96.6,
        offlineDevices: 2,
        offlineRate: 3.4,
        warningDevices: 1,
        abnormalDevices: 0,
        todayAlarms: 1,
        processedAlarms: 1,
        deviceTypes: [
          { name: '投喂机', total: 12, normal: 11, warning: 1, abnormal: 0, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 8, normal: 8, warning: 0, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 6, normal: 6, warning: 0, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 1, normal: 1, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      },
      10: {
        totalDevices: 45,
        deviceIncrease: 0,
        onlineDevices: 44,
        onlineRate: 97.8,
        offlineDevices: 1,
        offlineRate: 2.2,
        warningDevices: 0,
        abnormalDevices: 0,
        todayAlarms: 0,
        processedAlarms: 0,
        deviceTypes: [
          { name: '投喂机', total: 10, normal: 10, warning: 0, abnormal: 0, icon: 'fa fa-cubes' },
          { name: '摄像头', total: 6, normal: 6, warning: 0, abnormal: 0, icon: 'fa fa-video-camera' },
          { name: '增氧机', total: 4, normal: 4, warning: 0, abnormal: 0, icon: 'fa fa-leaf' },
          { name: '水泵', total: 3, normal: 3, warning: 0, abnormal: 0, icon: 'fa fa-tint' },
          { name: '水阀', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-toggle-on' },
          { name: '臭氧', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-cloud' },
          { name: '风机', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-fan' },
          { name: '超滤', total: 2, normal: 2, warning: 0, abnormal: 0, icon: 'fa fa-filter' },
          { name: '紫外线', total: 1, normal: 1, warning: 0, abnormal: 0, icon: 'fa fa-sun-o' }
        ]
      }
    });
    
    // 计算属性
    const selectedFloorName = computed(() => {
      // 查找当前选中的楼层
      for (const building of buildings) {
        const floor = building.floors.find(f => f.id === selectedFloor.value);
        if (floor) {
          return `${building.name}${floor.name}`;
        }
      }
      return '第001号楼第01层';
    });
    
    const currentFloorData = computed(() => {
      // 根据选中的楼栋返回不同的数据
      const buildingId = selectedBuilding.value;
      
      // 这里可以根据楼栋ID返回不同的数据
      // 例如：不同楼栋有不同的设备配置、池数等
      if (buildingId === 1) {
        // 第001号楼的数据
        return {
          ...floorData[1],
          buildingName: '第001号楼',
          totalPools: 24,
          buildingType: '智能养殖中心'
        };
      } else if (buildingId === 2) {
        // 第002号楼的数据
        return {
          ...floorData[2],
          buildingName: '第002号楼',
          totalPools: 18,
          buildingType: '育苗培育中心'
        };
      } else if (buildingId === 3) {
        // 第003号楼的数据
        return {
          ...floorData[3],
          buildingName: '第003号楼',
          totalPools: 20,
          buildingType: '成虾养殖中心'
        };
      } else if (buildingId === 4) {
        // 第004号楼的数据
        return {
          ...floorData[4],
          buildingName: '第004号楼',
          totalPools: 22,
          buildingType: '水质处理中心'
        };
      } else if (buildingId === 5) {
        // 第005号楼的数据
        return {
          ...floorData[5],
          buildingName: '第005号楼',
          totalPools: 25,
          buildingType: '饲料加工中心'
        };
      } else if (buildingId === 6) {
        // 第006号楼的数据
        return {
          ...floorData[6],
          buildingName: '第006号楼',
          totalPools: 28,
          buildingType: '仓储物流中心'
        };
      } else if (buildingId === 7) {
        // 第007号楼的数据
        return {
          ...floorData[7],
          buildingName: '第007号楼',
          totalPools: 30,
          buildingType: '研发实验中心'
        };
      } else if (buildingId === 8) {
        // 第008号楼的数据
        return {
          ...floorData[8],
          buildingName: '第008号楼',
          totalPools: 32,
          buildingType: '培训教育中心'
        };
      } else if (buildingId === 9) {
        // 第009号楼的数据
        return {
          ...floorData[9],
          buildingName: '第009号楼',
          totalPools: 35,
          buildingType: '管理办公中心'
        };
      } else {
        // 其他楼栋复用第010号楼的示例数据，但名称随选择变化
        const idStr = String(buildingId).padStart(3, '0');
        return {
          ...floorData[10],
          buildingName: `第${idStr}号楼`,
          totalPools: 38,
          buildingType: '综合服务中心'
        };
      }
    });
    
    // 设备数据
    const devices = reactive([
      {
        name: '增氧机A01',
        pool: 'A01池',
        status: '正常运行',
        maintenanceDate: '2023-07-01',
        action: '详情',
        icon: 'fa fa-bolt',
        bgColor: 'bg-blue-100',
        textColor: 'text-primary',
        statusBg: 'bg-green-100',
        statusText: 'text-green-800'
      },
      {
        name: '投饵机B02',
        pool: 'B02池',
        status: '轻微异常',
        maintenanceDate: '2023-06-25',
        action: '详情',
        icon: 'fa fa-ship',
        bgColor: 'bg-orange-100',
        textColor: 'text-orange-600',
        statusBg: 'bg-yellow-100',
        statusText: 'text-yellow-800'
      },
      {
        name: '监控摄像头C03',
        pool: 'C03池',
        status: '正常运行',
        maintenanceDate: '2023-07-10',
        action: '详情',
        icon: 'fa fa-video-camera',
        bgColor: 'bg-purple-100',
        textColor: 'text-purple-600',
        statusBg: 'bg-green-100',
        statusText: 'text-green-800'
      },
      {
        name: '水泵B04',
        pool: 'B04池',
        status: '已停止运行',
        maintenanceDate: '2023-06-15',
        action: '维修',
        icon: 'fa fa-tint',
        bgColor: 'bg-red-100',
        textColor: 'text-red-600',
        statusBg: 'bg-red-100',
        statusText: 'text-red-800'
      }
    ]);
    
    // 预警数据
    const alerts = reactive([
      {
        title: 'B04池水泵故障',
        time: '08:45',
        message: 'B04池循环水泵已停止运行，需要立即维修。',
        button1Text: '处理中',
        button2Text: '详情',
        borderColor: 'border-danger',
        titleColor: 'text-danger',
        button1Class: 'bg-danger text-white rounded hover:bg-danger/90'
      },
      {
        title: 'A03池溶氧量偏低',
        time: '07:30',
        message: 'A03池溶氧量6.2mg/L，低于建议值6.5mg/L。',
        button1Text: '已处理',
        button2Text: '详情',
        borderColor: 'border-warning',
        titleColor: 'text-warning',
        button1Class: 'bg-gray-100 text-gray-600 rounded'
      },
      {
        title: 'C03池pH值波动',
        time: '昨天 19:20',
        message: 'C03池pH值在1小时内波动超过0.5，需关注。',
        button1Text: '已处理',
        button2Text: '详情',
        borderColor: 'border-warning',
        titleColor: 'text-warning',
        button1Class: 'bg-gray-100 text-gray-600 rounded'
      },
      {
        title: '养殖报告已生成',
        time: '昨天 15:00',
        message: '7月上半月养殖报告已生成，可查看详情。',
        button1Text: '已查看',
        button2Text: '查看报告',
        borderColor: 'border-info',
        titleColor: 'text-info',
        button1Class: 'bg-gray-100 text-gray-600 rounded'
      }
    ]);
    
    // 方法
    const toggleMobileMenu = () => {
      mobileMenuOpen.value = !mobileMenuOpen.value;
    };
    
    const selectFloor = (buildingId, floorId) => {
      selectedFloor.value = floorId;
      selectedBuilding.value = buildingId;
      // 楼层点击时切换到楼层信息页面
      currentPage.value = 'floorInfo';
      const building = buildings.find(b => b.id === buildingId);
      const floor = building?.floors.find(f => f.id === floorId);
      if (building && floor) {
        console.log(`切换到楼层信息页面: ${building.name}${floor.name}`);
        console.log(`传递参数: 楼栋ID=${buildingId}, 楼层ID=${floorId}`);
      }
    };

    const selectBuilding = (buildingId) => {
      selectedBuilding.value = buildingId;
      // 楼栋点击时切换回管理页面
      currentPage.value = 'management';
      // 自动展开当前选中的楼栋
      if (!expandedBuildings.value.includes(buildingId)) {
        expandedBuildings.value.push(buildingId);
      }
      console.log('切换到管理页面，选择楼栋:', buildingId);
      
      // 根据楼栋选择不同的楼层数据
      // 这里可以添加逻辑来切换不同楼栋的数据
      // 例如：currentFloorData.value = getBuildingData(buildingId);
    };

    const toggleBuilding = (buildingId) => {
      if (expandedBuildings.value.includes(buildingId)) {
        expandedBuildings.value = expandedBuildings.value.filter(id => id !== buildingId);
      } else {
        expandedBuildings.value.push(buildingId);
        // 展开后滚动到可视区域（桌面和移动端）
        nextTick(() => {
          const desktopEl = document.getElementById(`building-${buildingId}-desktop`);
          const mobileEl = document.getElementById(`building-${buildingId}-mobile`);
          const el = mobileMenuOpen.value ? mobileEl : desktopEl;
          if (el && typeof el.scrollIntoView === 'function') {
            el.scrollIntoView({ block: 'nearest', behavior: 'smooth' });
          }
        });
      }
    };
    
    const updateLastUpdateTime = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      
      lastUpdateTime.value = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    };
    
    const initGrowthStageChart = () => {
      if (growthStageChartInstance.value) {
        growthStageChartInstance.value.destroy();
        growthStageChartInstance.value = null;
      }
      growthStageChartInstance.value = new Chart(growthStageChart.value, {
        type: 'doughnut',
        data: {
          labels: ['幼虾 (1-20日龄)', '中虾 (21-40日龄)', '成虾 (41日龄以上)'],
          datasets: [{
            data: [35, 45, 20],
            backgroundColor: [
              '#165DFF',
              '#00B42A',
              '#FF7D00'
            ],
            borderWidth: 0,
            hoverOffset: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          devicePixelRatio: 2,
          cutout: '70%',
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                boxWidth: 12,
                usePointStyle: true,
                pointStyle: 'circle',
                font: {
                  size: 16,  // 放大图例文字
                  weight: 'bold'  // 可选：加粗文字
                }
              }
            }
          }
        }
      });
    };
    
    const initWaterQualityChart = () => {
      if (waterQualityChartInstance.value) {
        waterQualityChartInstance.value.destroy();
        waterQualityChartInstance.value = null;
      }
      waterQualityChartInstance.value = new Chart(waterQualityChart.value, {
        type: 'line',
        data: {
          labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
          datasets: [{
            label: '水温(°C)',
            data: [25.8, 25.5, 26.1, 26.8, 26.5, 26.2],
            borderColor: '#165DFF',
            backgroundColor: 'rgba(22, 93, 255, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y'
          }, {
            label: '溶氧量(mg/L)',
            data: [6.8, 7.0, 6.5, 6.3, 6.2, 6.4],
            borderColor: '#722ED1',
            backgroundColor: 'rgba(114, 46, 209, 0.1)',
            tension: 0.4,
            fill: false,
            yAxisID: 'y1'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          devicePixelRatio: 2,
          plugins: {
            legend: {
              position: 'top',
              labels: {
                boxWidth: 10,
                usePointStyle: true,
                pointStyle: 'circle',
                font: {
                  size: 16
                }
              }
            },
            tooltip: {
              mode: 'index',
              intersect: false
            }
          },
          scales: {
            x: {
              grid: {
                display: false
              },
              ticks: {
                font: {
                  size: 16
                }
              }
            },
            y: {
              type: 'linear',
              display: true,
              position: 'left',
              min: 24,
              max: 28,
              ticks: {
                font: {
                  size: 16
                }
              },
              grid: {
                color: 'rgba(0, 0, 0, 0.05)'
              }
            },
            y1: {
              type: 'linear',
              display: true,
              position: 'right',
              min: 5,
              max: 8,
              ticks: {
                font: {
                  size: 16
                }
              },
              grid: {
                drawOnChartArea: false
              }
            }
          }
        }
      });
    };
    
    // 页面切换时处理图表
    watch(currentPage, async (val) => {
      if (val === 'management') {
        await nextTick();
        initGrowthStageChart();
        initWaterQualityChart();
      } else if (val === 'floorInfo') {
        if (growthStageChartInstance.value) {
          growthStageChartInstance.value.destroy();
          growthStageChartInstance.value = null;
        }
        if (waterQualityChartInstance.value) {
          waterQualityChartInstance.value.destroy();
          waterQualityChartInstance.value = null;
        }
      }
    });
    
    // 生命周期钩子
    onMounted(() => {
      // 初始化图表
      initGrowthStageChart();
      initWaterQualityChart();
      
      // 更新时间
      updateLastUpdateTime();
      setInterval(updateLastUpdateTime, 1000);
    });
    
    const buildingImages = import.meta.glob('/src/assets/images/Building/*.jpg', { eager: true, import: 'default' });
    const buildingImg = computed(() => {
      const idStr = String(selectedBuilding.value).padStart(2, '0');
      for (const [path, url] of Object.entries(buildingImages)) {
        if (path.endsWith(`/${idStr}.jpg`)) return url;
      }
      return buildingImages['/src/assets/images/Building/01.jpg'];
    });
    const getIconClass = (icon) => {
      if (icon === 'fa fa-fan') {
        // Font Awesome 4 不支持 fan，fallback 到 spinner
        return 'fa fa-spinner';
      }
      return icon;
    };
    return {
      mobileMenuOpen,
      lastUpdateTime,
      growthStageChart,
      waterQualityChart,
      growthStageChartInstance,
      waterQualityChartInstance,
      currentPage,
      floors,
      buildings,
      expandedBuildings,
      selectedFloor,
      selectedBuilding,
      selectedFloorName,
      currentFloorData,
      devices,
      alerts,
      toggleMobileMenu,
      selectFloor,
      selectBuilding,
      toggleBuilding,
      getIconClass,
      buildingImg
    };
  }
};
</script>

<style scoped>
/* 基础动画 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滚动条美化 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 养殖池动画效果 */
.pool-item {
  transition: all 0.3s ease;
}

.pool-item:hover {
  transform: scale(1.05);
}

/* 数据刷新动画 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.pulse-animation {
  animation: pulse 2s infinite;
}
</style>
