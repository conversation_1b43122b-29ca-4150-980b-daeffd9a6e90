<template>
  <div class="relative h-full w-full" ref="container">
    <!-- 楼栋弹窗 -->
    <div v-if="showBuildingPopup" class="building-popup" :style="popupStyle">
      <div class="popup-header">
        <h3 class="popup-title">{{ popupData.title }}</h3>
        <button @click="closeBuildingPopup" class="close-btn">×</button>
      </div>
      <div class="popup-content" v-show="popupData.type === 'aquaculture'">
        <div class="data-item">
          <span class="label">当前耗电量:</span>
          <span class="value">{{ popupData.powerConsumption }} kWh</span>
        </div>
        <div class="data-item">
          <span class="label">养殖池数:</span>
          <span class="value">324 个</span>
        </div>
        <div class="data-item">
          <span class="label">预计产量:</span>
          <span class="value">{{ popupData.expectedOutput }} kg</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader.js";
import { DRACOLoader } from "three/examples/jsm/loaders/DRACOLoader.js";
// 导入轨道控制器
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls";
// import { GUI } from "three/examples/jsm/libs/lil-gui.module.min.js";

// 定义事件发射器
const emit = defineEmits<{
  "switch-to-dashboard2": [floorName: string];
}>();

// 获取容器引用
const container = ref<HTMLDivElement>();

// 弹窗相关响应式变量
const showBuildingPopup = ref(false);
const popupStyle = ref({});
const popupData = ref({
  title: "",
  type: "aquaculture", // 'aquaculture' 或 'technology'
  powerConsumption: "",
  expectedOutput: "",
});

// Three.js 相关变量
let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let poolModel: THREE.Group | null = null;
let animationId: number;
let controls: OrbitControls;
// let gui: GUI;
let raycaster: THREE.Raycaster;
let mouse: THREE.Vector2;
let building1Object: THREE.Object3D | null = null; // 存储1栋楼的对象引用
let lastUpdateTime = 0; // 上次更新弹窗位置的时间
let lastPopupPosition = { x: 0, y: 0 }; // 上次弹窗位置

// 自动轮播相关变量
let carouselInterval: number | null = null;
let currentBuildingIndex = 0;
const buildingIds = [1, 2, 3, 4, 5, 6, 7, 8, 9]; // 1-9栋养殖楼
let isManualClick = false; // 标记是否为手动点击

// 初始化 Three.js
function initThreeJS() {
  if (!container.value) return;

  // 获取容器尺寸
  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  // 创建场景
  scene = new THREE.Scene();

  // 创建相机
  camera = new THREE.PerspectiveCamera(
    45, // 视角
    width / height, // 宽高比
    0.1, // 近平面
    1000 // 远平面
  );

  // 创建渲染器
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: true, // 启用透明度支持
  });
  renderer.setSize(width, height);
  renderer.setPixelRatio(window.devicePixelRatio);
  renderer.setClearColor(0x000000, 0); // 设置背景为透明

  // 将渲染器添加到容器中
  container.value.appendChild(renderer.domElement);

  // 初始化射线投射器和鼠标向量
  raycaster = new THREE.Raycaster();
  mouse = new THREE.Vector2();

  // 添加鼠标点击事件监听器
  renderer.domElement.addEventListener("click", onMouseClick, false);

  // 添加光源
  const ambientLight = new THREE.AmbientLight(0xffffff, 1.0);
  scene.add(ambientLight);

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
  directionalLight.position.set(10, 10, 5);
  scene.add(directionalLight);

  // 加载FBX模型
  loadPoolModel();

  // 设置相机位置
  camera.position.set(17.44, 12.46, -2.53);
  camera.lookAt(3.44, 1.65, -11.62);

  // 创建轨道控制器
  controls = new OrbitControls(camera, renderer.domElement);

  // 设置控制器的目标点与lookAt一致
  controls.target.set(3.44, 1.65, -11.62);
  controls.update();

  // 创建相机调试GUI（已注释）
  // setupCameraDebugGUI();

  // 开始动画
  animate();
}

// 设置相机调试GUI（已注释）
// function setupCameraDebugGUI() {
//   gui = new GUI();

//   // 相机位置参数
//   const cameraParams = {
//     positionX: camera.position.x,
//     positionY: camera.position.y,
//     positionZ: camera.position.z,
//     targetX: 0,
//     targetY: 0,
//     targetZ: 0,
//     // 显示当前相机信息的函数
//     logCameraInfo() {
//       console.log("=== 当前相机参数 ===");
//       console.log(
//         `相机位置: camera.position.set(${camera.position.x.toFixed(
//           2
//         )}, ${camera.position.y.toFixed(2)}, ${camera.position.z.toFixed(2)});`
//       );
//       console.log(
//         `相机目标: camera.lookAt(${controls.target.x.toFixed(
//           2
//         )}, ${controls.target.y.toFixed(2)}, ${controls.target.z.toFixed(2)});`
//       );
//       console.log(
//         `控制器目标: controls.target.set(${controls.target.x.toFixed(
//           2
//         )}, ${controls.target.y.toFixed(2)}, ${controls.target.z.toFixed(2)});`
//       );
//     },
//     // 重置相机位置
//     resetCamera() {
//       camera.position.set(0, 1, 0.7);
//       controls.target.set(0, 0.3, 0);
//       controls.update();
//       updateGUIValues();
//     },
//     // 全屏切换
//     toggleFullscreen() {
//       if (!document.fullscreenElement) {
//         document.body.requestFullscreen();
//       } else {
//         document.exitFullscreen();
//       }
//     },
//   };

//   // 更新GUI显示值的函数
//   function updateGUIValues() {
//     cameraParams.positionX = camera.position.x;
//     cameraParams.positionY = camera.position.y;
//     cameraParams.positionZ = camera.position.z;
//     cameraParams.targetX = controls.target.x;
//     cameraParams.targetY = controls.target.y;
//     cameraParams.targetZ = controls.target.z;
//     // 手动更新GUI控制器显示
//     gui
//       .controllersRecursive()
//       .forEach((controller) => controller.updateDisplay());
//   }

//   // 相机位置控制
//   const cameraFolder = gui.addFolder("相机位置");
//   cameraFolder
//     .add(cameraParams, "positionX", -20, 20, 0.1)
//     .onChange((value) => {
//       camera.position.x = value;
//     });
//   cameraFolder
//     .add(cameraParams, "positionY", -20, 20, 0.1)
//     .onChange((value) => {
//       camera.position.y = value;
//     });
//   cameraFolder
//     .add(cameraParams, "positionZ", -20, 20, 0.1)
//     .onChange((value) => {
//       camera.position.z = value;
//     });

//   // 相机目标控制
//   const targetFolder = gui.addFolder("相机目标");
//   targetFolder.add(cameraParams, "targetX", -10, 10, 0.1).onChange((value) => {
//     controls.target.x = value;
//     controls.update();
//   });
//   targetFolder.add(cameraParams, "targetY", -10, 10, 0.1).onChange((value) => {
//     controls.target.y = value;
//     controls.update();
//   });
//   targetFolder.add(cameraParams, "targetZ", -10, 10, 0.1).onChange((value) => {
//     controls.target.z = value;
//     controls.update();
//   });

//   // 功能按钮
//   gui.add(cameraParams, "logCameraInfo").name("📋 获取当前参数");
//   gui.add(cameraParams, "resetCamera").name("🔄 重置相机");
//   gui.add(cameraParams, "toggleFullscreen").name("🖥️ 全屏切换");

//   // 监听控制器变化，实时更新GUI显示
//   controls.addEventListener("change", updateGUIValues);

//   // 展开文件夹
//   cameraFolder.open();
//   targetFolder.open();
// }

// 弹窗相关函数
function closeBuildingPopup() {
  showBuildingPopup.value = false;

  // 如果是手动点击后关闭，恢复轮播
  if (isManualClick) {
    isManualClick = false;
    // 延迟一点时间再恢复轮播，让用户看到弹窗已关闭
    setTimeout(() => {
      startCarousel(true); // 从当前位置恢复轮播
    }, 500);
  }
}

// 开始自动轮播
function startCarousel(resumeFromCurrent = false) {
  // 先停止之前的轮播
  stopCarousel();

  // 如果不是从当前位置恢复，则立即显示当前楼栋
  if (!resumeFromCurrent) {
    showNextBuilding();
  }

  // 设置定时器，每5秒切换一次
  carouselInterval = setInterval(() => {
    showNextBuilding();
  }, 5000);
}

// 停止自动轮播
function stopCarousel() {
  if (carouselInterval) {
    clearInterval(carouselInterval);
    carouselInterval = null;
  }
}

// 显示下一个楼栋
function showNextBuilding() {
  const buildingId = buildingIds[currentBuildingIndex];

  // 生成楼栋信息
  const buildingInfo = {
    title: `${buildingId}栋养殖楼`,
    type: "aquaculture",
    buildingId: buildingId,
    powerConsumption: (1200 + Math.floor(Math.random() * 300)).toLocaleString(),
    expectedOutput: (2800 + Math.floor(Math.random() * 800)).toLocaleString(),
  };

  // 显示弹窗
  showBuildingPopupWithInfo(buildingInfo);

  // 移动到下一个楼栋
  currentBuildingIndex = (currentBuildingIndex + 1) % buildingIds.length;
}

// 显示楼栋弹窗
function showBuildingPopupWithInfo(buildingInfo: any) {
  if (!camera || !renderer || !container.value) return;

  // 设置弹窗数据
  popupData.value = buildingInfo;

  // 查找对应的楼栋对象
  const buildingObject = findBuildingObject(buildingInfo);
  if (buildingObject) {
    building1Object = buildingObject; // 复用现有的变量来存储当前选中的楼栋
  }

  // 重置位置记录，避免闪烁
  lastPopupPosition = { x: 0, y: 0 };

  showBuildingPopup.value = true;
  // 位置更新将在animate函数中的updatePopupPosition中处理
}

// 获取楼栋信息
function getBuildingInfo(object: THREE.Object3D): any {
  // 检查是否是养殖楼（1-9栋）
  const buildingId = getBuildingId(object);
  if (buildingId !== null && buildingId >= 1 && buildingId <= 9) {
    return {
      title: `${buildingId}栋养殖楼`,
      type: "aquaculture",
      buildingId: buildingId,
      powerConsumption: (
        1200 + Math.floor(Math.random() * 300)
      ).toLocaleString(), // 1200-1500 kWh
      expectedOutput: (2800 + Math.floor(Math.random() * 800)).toLocaleString(), // 2800-3600 kg (养虾产量)
    };
  }

  // 检查是否是科技楼
  if (isTechnologyBuilding(object)) {
    return {
      title: "科技楼",
      type: "technology",
      buildingId: null,
    };
  }

  return null;
}

// 获取楼栋ID
function getBuildingId(object: THREE.Object3D): number | null {
  // 检查当前对象的userData.building_id
  if (object.userData && typeof object.userData.building_id === "number") {
    return object.userData.building_id;
  }

  // 检查父级对象的userData.building_id
  let parent = object.parent;
  while (parent) {
    if (parent.userData && typeof parent.userData.building_id === "number") {
      return parent.userData.building_id;
    }
    parent = parent.parent;
  }

  return null;
}

// 检查是否是科技楼
function isTechnologyBuilding(object: THREE.Object3D): boolean {
  // 检查父级对象的userData.name
  let parent = object.parent;
  while (parent) {
    if (
      parent.userData &&
      parent.userData.name &&
      parent.userData.name.toLowerCase().includes("科技")
    ) {
      return true;
    }
    parent = parent.parent;
  }

  return false;
}

// 查找楼栋对象
function findBuildingObject(buildingInfo: any): THREE.Object3D | null {
  if (!poolModel) return null;

  if (buildingInfo.type === "aquaculture") {
    // 查找养殖楼的attic对象
    return findAtticByBuildingId(poolModel, buildingInfo.buildingId);
  } else if (buildingInfo.type === "technology") {
    // 查找科技楼对象
    return findTechnologyBuildingRecursive(poolModel);
  }

  return null;
}

// 查找指定楼栋ID的attic对象
function findAtticByBuildingId(
  object: THREE.Object3D,
  buildingId: number
): THREE.Object3D | null {
  // 检查当前对象是否有attic属性且building_id匹配
  if (
    object.userData &&
    object.userData.attic &&
    object.userData.building_id === buildingId
  ) {
    return object;
  }

  // 递归检查子对象
  for (const child of object.children) {
    const result = findAtticByBuildingId(child, buildingId);
    if (result) {
      return result;
    }
  }

  return null;
}

// 递归查找指定ID的楼栋
function findBuildingByIdRecursive(
  object: THREE.Object3D,
  buildingId: number
): THREE.Object3D | null {
  if (object.userData && object.userData.building_id === buildingId) {
    return object;
  }

  for (const child of object.children) {
    const result = findBuildingByIdRecursive(child, buildingId);
    if (result) {
      return result;
    }
  }

  return null;
}

// 递归查找科技楼
function findTechnologyBuildingRecursive(
  object: THREE.Object3D
): THREE.Object3D | null {
  if (
    object.userData &&
    object.userData.name &&
    object.userData.name.toLowerCase().includes("科技")
  ) {
    return object;
  }

  for (const child of object.children) {
    const result = findTechnologyBuildingRecursive(child);
    if (result) {
      return result;
    }
  }

  return null;
}

// 查找1栋楼对象（保留向后兼容）
function findBuilding1Object(object: THREE.Object3D): THREE.Object3D | null {
  return findBuildingByIdRecursive(object, 1);
}

// 鼠标点击事件处理函数
function onMouseClick(event: MouseEvent) {
  if (!container.value || !camera || !scene) return;

  // 计算鼠标位置（标准化设备坐标）
  const rect = container.value.getBoundingClientRect();
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

  // 更新射线投射器
  raycaster.setFromCamera(mouse, camera);

  // 检测与模型的交集
  if (poolModel) {
    const intersects = raycaster.intersectObjects(poolModel.children, true);

    if (intersects.length > 0) {
      const clickedObject = intersects[0].object;
      console.log("🚀 ~ onMouseClick ~ clickedObject:", clickedObject);
      console.log("🚀 ~ userData:", clickedObject.userData);

      // 优先检查是否点击了楼层（层数优先级更高）
      if (isBuildingFloor(clickedObject)) {
        const floorName = getFloorName(clickedObject);
        console.log("点击了楼层:", floorName);
        // 发射事件切换到dashboard2，传递楼层名字
        emit("switch-to-dashboard2", floorName);
        return;
      }

      // 然后检查是否点击了养殖楼（1-9栋）或科技楼
      const buildingInfo = getBuildingInfo(clickedObject);
      if (buildingInfo) {
        console.log(`点击了${buildingInfo.title}，显示弹窗`);

        // 设置轮播恢复位置
        if (buildingInfo.type === "aquaculture") {
          // 如果点击的是养殖楼，从点击的楼栋开始轮播
          const clickedIndex = buildingIds.indexOf(buildingInfo.buildingId);
          if (clickedIndex !== -1) {
            currentBuildingIndex = clickedIndex;
          }
        } else {
          // 如果点击的是科技楼，从1栋重新开始轮播
          currentBuildingIndex = 0;
        }

        // 手动点击时停止自动轮播并标记为手动点击
        isManualClick = true;
        stopCarousel();
        showBuildingPopupWithInfo(buildingInfo);
        return;
      }
    }
  }
}

// 检查对象是否是1栋楼
function isBuilding1(object: THREE.Object3D): boolean {
  // 检查当前对象的userData.building_id
  if (object.userData && object.userData.building_id === 1) {
    return true;
  }

  // 检查父级对象的userData.building_id
  let parent = object.parent;
  while (parent) {
    if (parent.userData && parent.userData.building_id === 1) {
      return true;
    }
    parent = parent.parent;
  }

  return false;
}

// 检查对象是否是楼层
function isBuildingFloor(object: THREE.Object3D): boolean {
  // 检查对象名称是否包含楼层标识
  const name = object.name.toLowerCase();

  // 匹配 "1栋2层"、"1栋3层" 等格式
  const floorPattern = /\d+栋\d+层/;

  // 检查当前对象名称
  if (floorPattern.test(name)) {
    return true;
  }

  // 检查父级对象名称
  let parent = object.parent;
  while (parent) {
    if (floorPattern.test(parent.name.toLowerCase())) {
      return true;
    }
    parent = parent.parent;
  }

  return false;
}

// 获取楼层名称
function getFloorName(object: THREE.Object3D): string {
  // 检查对象名称是否包含楼层标识
  const name = object.name.toLowerCase();

  // 匹配 "1栋2层"、"1栋3层" 等格式
  const floorPattern = /(\d+栋\d+层)/;

  // 检查当前对象名称
  const match = name.match(floorPattern);
  if (match) {
    return match[1];
  }

  // 检查父级对象名称
  let parent = object.parent;
  while (parent) {
    const parentMatch = parent.name.toLowerCase().match(floorPattern);
    if (parentMatch) {
      return parentMatch[1];
    }
    parent = parent.parent;
  }

  // 如果没有找到匹配的楼层名称，返回默认值
  return "1栋1层";
}

// 打印楼栋信息（用于调试）
function printBuildingInfo(object: THREE.Object3D, depth = 0) {
  const indent = "  ".repeat(depth);

  // 检查是否有userData
  if (object.userData && Object.keys(object.userData).length > 0) {
    console.log(
      `${indent}${object.name || "unnamed"} - userData:`,
      object.userData
    );

    // 检查是否是楼栋
    const buildingInfo = getBuildingInfo(object);
    if (buildingInfo) {
      console.log(`${indent}  -> 识别为: ${buildingInfo.title}`);
    }

    // 检查是否有attic属性
    if (object.userData.attic) {
      console.log(
        `${indent}  -> 有attic属性，building_id: ${object.userData.building_id}`
      );
    }
  }

  // 递归检查子对象
  object.children.forEach((child) => {
    printBuildingInfo(child, depth + 1);
  });
}

// 打印所有attic对象（用于调试）
function printAtticObjects(object: THREE.Object3D, depth = 0) {
  const indent = "  ".repeat(depth);

  // 检查是否有attic属性
  if (object.userData && object.userData.attic) {
    console.log(
      `${indent}找到attic对象: ${object.name || "unnamed"}`,
      `building_id: ${object.userData.building_id}`,
      `位置:`,
      object.position,
      `userData:`,
      object.userData
    );
  }

  // 递归检查子对象
  object.children.forEach((child) => {
    printAtticObjects(child, depth + 1);
  });
}

// 打印模型结构（用于调试）
function printModelStructure(object: THREE.Object3D, depth = 0) {
  const indent = "  ".repeat(depth);
  console.log(`${indent}${object.name || "unnamed"} (${object.type})`);

  // 递归打印子对象
  object.children.forEach((child) => {
    printModelStructure(child, depth + 1);
  });
}

// 加载GLB模型
async function loadPoolModel() {
  const loader = new GLTFLoader();
  const dracoLoader = new DRACOLoader();
  dracoLoader.setDecoderPath("/draco/");
  // 设置gltf加载器draco解码器
  loader.setDRACOLoader(dracoLoader);
  loader.load(
    "/yuanqu.glb",
    (gltf) => {
      // 相同的加载逻辑...
      poolModel = gltf.scene;
      gltf.scene.scale.setScalar(0.07);
      gltf.scene.position.set(0, 0, 0);
      gltf.scene.rotation.y = Math.PI / 4;
      scene.add(gltf.scene);
      console.log("Model loaded from fallback path");
      // 启动轮播
      setTimeout(() => {
        startCarousel();
      }, 1000);
    },
    undefined,
    (error) => {
      console.error("Error loading fallback model:", error);
    }
  );
}

// 更新弹窗位置
function updatePopupPosition() {
  if (
    !showBuildingPopup.value ||
    !building1Object ||
    !camera ||
    !renderer ||
    !container.value
  )
    return;

  // 获取attic对象的世界坐标位置
  const buildingPosition = new THREE.Vector3();
  building1Object.getWorldPosition(buildingPosition);

  // 在attic对象上方添加一些偏移
  buildingPosition.y += 1; // 向上偏移1个单位，因为attic已经在楼顶了

  // 将3D坐标转换为屏幕坐标
  const screenPosition = buildingPosition.clone().project(camera);

  // 转换为像素坐标
  const rect = container.value.getBoundingClientRect();
  const x = (screenPosition.x * 0.5 + 0.5) * rect.width;
  const y = (screenPosition.y * -0.5 + 0.5) * rect.height;

  // 计算弹窗位置并四舍五入避免小数像素
  const finalX = Math.round(x - 100); // 弹窗宽度的一半，使其居中
  const finalY = Math.round(y - 220); // 向上移动，使其在attic上方

  // 检查位置是否有显著变化（避免微小抖动）
  const positionChanged =
    Math.abs(finalX - lastPopupPosition.x) > 1 ||
    Math.abs(finalY - lastPopupPosition.y) > 1;

  // 只有位置变化超过阈值时才更新
  if (positionChanged) {
    lastPopupPosition.x = finalX;
    lastPopupPosition.y = finalY;

    // 设置弹窗位置
    popupStyle.value = {
      position: "absolute",
      left: `${finalX}px`,
      top: `${finalY}px`,
      zIndex: 1000,
    };
  }
}

// 渲染函数
function animate() {
  animationId = requestAnimationFrame(animate);

  // 节流更新弹窗位置，每200ms最多更新一次
  const currentTime = Date.now();
  if (
    showBuildingPopup.value &&
    building1Object &&
    currentTime - lastUpdateTime > 200
  ) {
    updatePopupPosition();
    lastUpdateTime = currentTime;
  }

  renderer.render(scene, camera);
}

// 处理窗口大小变化
function handleResize() {
  if (!container.value || !camera || !renderer) return;

  const width = container.value.clientWidth;
  const height = container.value.clientHeight;

  camera.aspect = width / height;
  camera.updateProjectionMatrix();
  renderer.setSize(width, height);
}

// 清理资源
function cleanup() {
  if (animationId) {
    cancelAnimationFrame(animationId);
  }

  // 清理模型
  if (poolModel) {
    scene.remove(poolModel);
    poolModel = null;
  }

  // 清理GUI（已注释）
  // if (gui) {
  //   gui.destroy();
  // }

  // 清理控制器
  if (controls) {
    controls.dispose();
  }

  // 移除鼠标事件监听器
  if (renderer && renderer.domElement) {
    renderer.domElement.removeEventListener("click", onMouseClick, false);
  }

  if (renderer) {
    renderer.dispose();
  }

  window.removeEventListener("resize", handleResize);

  // 清理轮播定时器
  stopCarousel();
}

// 组件挂载时初始化
onMounted(() => {
  initThreeJS();
  window.addEventListener("resize", handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  cleanup();
});
</script>

<style scoped>
.building-popup {
  width: 200px;
  background: linear-gradient(
    135deg,
    rgba(30, 58, 138, 0.9) 0%,
    rgba(59, 130, 246, 0.8) 100%
  );
  border-radius: 8px;
  border: 1px solid rgba(102, 204, 255, 0.5);
  /* backdrop-filter: blur(20px); */
  box-shadow: 0 6px 24px rgba(102, 204, 255, 0.2);
  color: white;
  font-family: "Microsoft YaHei", sans-serif;
  animation: popupFadeIn 0.3s ease-out;
  position: relative;
  overflow: hidden;
}

.building-popup::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    #66ccff 50%,
    transparent 100%
  );
}

@keyframes popupFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px 8px 12px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
}

.popup-title {
  margin: 0;
  font-size: 14px;
  font-weight: bold;
  color: #66ccff;
}

.close-btn {
  background: none;
  border: none;
  color: #66ccff;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
  border: 1px solid rgba(102, 204, 255, 0.3);
}

.close-btn:hover {
  background: rgba(102, 204, 255, 0.2);
  border-color: #66ccff;
  transform: scale(1.1);
}

.popup-content {
  padding: 10px 12px 12px 12px;
}

.data-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 6px 8px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(102, 204, 255, 0.3);
  transition: all 0.3s ease;
}

.data-item:last-child {
  margin-bottom: 0;
}

.data-item:hover {
  background: rgba(0, 0, 0, 0.5);
  border-color: rgba(102, 204, 255, 0.2);
  transform: translateX(2px);
}

.label {
  font-size: 12px;
  color: #e2e8f0;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.value {
  font-size: 12px;
  color: #ffffff;
  font-weight: 600;
  letter-spacing: 0.3px;
}

/* 数据项动画效果 */
.data-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(180deg, #66ccff, #33aaff);
  border-radius: 0 2px 2px 0;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.data-item {
  position: relative;
}

.data-item:hover::before {
  opacity: 1;
}

/* 弹窗整体悬停效果 */
.building-popup:hover {
  border-color: rgba(102, 204, 255, 0.5);
}
</style>
