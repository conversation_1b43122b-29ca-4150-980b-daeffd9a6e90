export interface ProductionItem {
  factory: string; // 厂区
  workshop: string; // 车间
  poolNumber: string; // 水池编号
  species: string; // 饲养品种
}

// 生成1-19栋的生产数据
export const generateBuildingData = (buildingId: number): ProductionItem[] => {
  const workshops = ["1车间", "2车间", "3车间", "4车间", "5车间", "6车间"];
  const poolPrefixes = ["A", "B", "C", "D", "E", "F"];
  const species = [
    "草鱼",
    "鲢鱼",
    "鳙鱼",
    "鲤鱼",
    "鲫鱼",
    "青鱼",
    "黑鱼",
    "鲈鱼",
    "桂鱼",
    "鳜鱼",
    "罗非鱼",
    "鲶鱼",
  ];

  const data: ProductionItem[] = [];

  // 为每个楼栋生成20-30条数据
  const itemCount = 20 + Math.floor(Math.random() * 10);

  // 设置随机种子，确保相同楼栋生成相同数据
  const seed = buildingId * 1000;
  let randomSeed = seed;

  const seededRandom = () => {
    randomSeed = (randomSeed * 9301 + 49297) % 233280;
    return randomSeed / 233280;
  };

  for (let i = 0; i < itemCount; i++) {
    const poolPrefix =
      poolPrefixes[Math.floor(seededRandom() * poolPrefixes.length)];
    const poolNumber = Math.floor(seededRandom() * 99) + 1; // 1-99

    data.push({
      factory: `${buildingId}号厂区`,
      workshop: workshops[Math.floor(seededRandom() * workshops.length)],
      poolNumber: `${poolPrefix}${poolNumber.toString().padStart(2, "0")}`,
      species: species[Math.floor(seededRandom() * species.length)],
    });
  }

  return data;
};

// 预生成1-19栋的所有数据
export const allBuildingsData: Record<number, ProductionItem[]> = {};

// 初始化所有楼栋数据
for (let buildingId = 1; buildingId <= 19; buildingId++) {
  allBuildingsData[buildingId] = generateBuildingData(buildingId);
}

// 获取指定楼栋的数据
export const getBuildingData = (buildingId: number): ProductionItem[] => {
  return allBuildingsData[buildingId] || [];
};

// 获取所有楼栋数据
export const getAllBuildingsData = (): Record<number, ProductionItem[]> => {
  return allBuildingsData;
};

// 楼栋统计信息
export const getBuildingStats = (buildingId: number) => {
  const data = getBuildingData(buildingId);
  if (!data.length) return null;

  return {
    totalItems: data.length,
    workshops: [...new Set(data.map((item) => item.workshop))].sort(),
    poolNumbers: [...new Set(data.map((item) => item.poolNumber))].sort(),
    species: [...new Set(data.map((item) => item.species))].sort(),
  };
};

// 所有楼栋的统计信息
export const getAllBuildingsStats = () => {
  const stats: Record<number, any> = {};
  for (let buildingId = 1; buildingId <= 19; buildingId++) {
    stats[buildingId] = getBuildingStats(buildingId);
  }
  return stats;
};
