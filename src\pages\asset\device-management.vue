<template>
  <div class="min-h-screen text-gray-100" style="background: linear-gradient(135deg, rgb(3 54 104) 0%, rgb(0, 51, 102) 50%, rgb(0, 68, 136) 100%)">
    <!-- 顶部导航 -->
    <header class="bg-white/5 backdrop-blur border-b border-white/10 sticky top-0 z-10">
      <div class="w-full px-4 py-4 flex justify-center items-center">
        <h1 class="text-2xl font-bold text-cyan-300 " >设备监控</h1>        
      </div>
    </header>

    <main class="w-full px-4 py-6">

      <!-- 设备概览统计 -->
      <section class="mb-8">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <i class="fa fa-tachometer mr-2 text-cyan-300"></i>设备概览统计
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <!-- 设备总数 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="text-sm text-white/70 mb-1">设备总数</div>
            <div class="text-3xl font-bold mb-1">186</div>
            <div class="text-base text-green-500 flex items-center">
              <i class="fa fa-arrow-up mr-1"></i>较昨日 +5
            </div>
          </div>

          <!-- 在线设备 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="text-sm text-white/70 mb-1">在线设备</div>
            <div class="text-3xl font-bold mb-1">179</div>
            <div class="text-base text-green-500 flex items-center">
              在线率96.2%
            </div>
          </div>

          <!-- 离线设备 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="text-sm text-white/70 mb-1">离线设备</div>
            <div class="text-3xl font-bold mb-1">7</div>
            <div class="text-base text-white/70 flex items-center">
              离线率3.8%
            </div>
          </div>

          <!-- 告警设备 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="text-sm text-white/70 mb-1">告警设备</div>
            <div class="text-3xl font-bold mb-1">4</div>
            <div class="text-base text-yellow-500 flex items-center">
              需要关注
            </div>
          </div>

          <!-- 异常设备 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="text-sm text-white/70 mb-1">异常设备</div>
            <div class="text-3xl font-bold mb-1">2</div>
            <div class="text-base text-orange-500 flex items-center">
              需要立即处理
            </div>
          </div>

          <!-- 今日告警 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="text-sm text-white/70 mb-1">今日告警</div>
            <div class="text-3xl font-bold mb-1">12</div>
            <div class="text-base text-green-500 flex items-center">
              已处理
            </div>
          </div>
        </div>
      </section>

      <!-- 设备类型分布 -->
      <section class="mb-8">
        <h3 class="text-lg font-semibold mb-4 flex items-center">
          <i class="fa fa-pie-chart mr-2 text-cyan-300"></i>设备类型分布
        </h3>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          <!-- 投饵机 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="flex justify-between items-start mb-2">
              <div>
                <div class="font-medium">投饵机</div>
                <div class="text-sm text-white/70">42台</div>
              </div>
              <div class="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <div class="flex justify-between items-center text-sm gap-3">
              <div class="flex items-center gap-1">
                <span class="text-white/80">正常</span>
                <span class="text-base text-green-500 font-semibold">51</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">预警</span>
                <span class="text-base text-orange-500 font-semibold">0</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">异常</span>
                <span class="text-base text-red-500 font-semibold">0</span>
              </div>
            </div>
          </div>

          <!-- 摄像头 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="flex justify-between items-start mb-2">
              <div>
                <div class="font-medium">摄像头</div>
                <div class="text-sm text-white/70">26台</div>
              </div>
              <div class="w-3 h-3 rounded-full bg-orange-500"></div>
            </div>
            <div class="flex justify-between items-center text-sm gap-3">
              <div class="flex items-center gap-1">
                <span class="text-white/80">正常</span>
                <span class="text-base text-green-500 font-semibold">75</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">预警</span>
                <span class="text-base text-orange-500 font-semibold">2</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">异常</span>
                <span class="text-base text-red-500 font-semibold">1</span>
              </div>
            </div>
          </div>

          <!-- 增氧机 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="flex justify-between items-start mb-2">
              <div>
                <div class="font-medium">增氧机</div>
                <div class="text-sm text-white/70">20台</div>
              </div>
              <div class="w-3 h-3 rounded-full bg-red-500"></div>
            </div>
            <div class="flex justify-between items-center text-sm gap-3">
              <div class="flex items-center gap-1">
                <span class="text-white/80">正常</span>
                <span class="text-base text-green-500 font-semibold">11</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">预警</span>
                <span class="text-base text-orange-500 font-semibold">0</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">异常</span>
                <span class="text-base text-red-500 font-semibold">9</span>
              </div>
            </div>
          </div>

          <!-- 水泵 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="flex justify-between items-start mb-2">
              <div>
                <div class="font-medium">水泵</div>
                <div class="text-sm text-white/70">12台</div>
              </div>
              <div class="w-3 h-3 rounded-full bg-red-500"></div>
            </div>
            <div class="flex justify-between items-center text-sm gap-3">
              <div class="flex items-center gap-1">
                <span class="text-white/80">正常</span>
                <span class="text-base text-green-500 font-semibold">41</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">预警</span>
                <span class="text-base text-orange-500 font-semibold">4</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">异常</span>
                <span class="text-base text-red-500 font-semibold">2</span>
              </div>
            </div>
          </div>

          <!-- 自动水阀 -->
          <div class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-4 hover:shadow-md transition-shadow backdrop-blur">
            <div class="flex justify-between items-start mb-2">
              <div>
                <div class="font-medium">自动水阀</div>
                <div class="text-sm text-white/70">12台</div>
              </div>
              <div class="w-3 h-3 rounded-full bg-green-500"></div>
            </div>
            <div class="flex justify-between items-center text-sm gap-3">
              <div class="flex items-center gap-1">
                <span class="text-white/80">正常</span>
                <span class="text-base text-green-500 font-semibold">32</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">预警</span>
                <span class="text-base text-orange-500 font-semibold">0</span>
              </div>
              <div class="flex items-center gap-1">
                <span class="text-white/80">异常</span>
                <span class="text-base text-red-500 font-semibold">0</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- 底部三栏布局 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- 操作日志 -->
        <section class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-5 backdrop-blur">
          <h3 class="text-lg font-semibold mb-4 flex items-center">
            <i class="fa fa-history mr-2 text-cyan-300"></i>操作日志
          </h3>
          <div class="space-y-4 max-h-[480px] overflow-y-auto pr-2">
            <div class="pb-3 border-b border-gray-100 dark:border-gray-700">
              <div class="text-sm">2025-10-10 13:24:32</div>
              <div class="text-white/80 text-sm mt-1">增氧机#001C1-21启动</div>
            </div>
            <div class="pb-3 border-b border-gray-100 dark:border-gray-700">
              <div class="text-sm">2025-10-10 13:15:47</div>
              <div class="text-white/80 text-sm mt-1">投饵机#001C1-twj-12完成投喂</div>
            </div>
            <div class="pb-3 border-b border-gray-100 dark:border-gray-700">
              <div class="text-sm">2025-10-10 12:58:21</div>
              <div class="text-white/80 text-sm mt-1">水泵#001C1-p-03关闭</div>
            </div>
            <div class="pb-3 border-b border-gray-100 dark:border-gray-700">
              <div class="text-sm">2025-10-10 12:45:10</div>
              <div class="text-gray-700 dark:text-gray-300 text-sm mt-1">自动水阀#001C1-v-07开启</div>
            </div>
            <div class="pb-3 border-b border-gray-100 dark:border-gray-700">
              <div class="text-sm">2025-10-10 12:30:05</div>
              <div class="text-gray-700 dark:text-gray-300 text-sm mt-1">摄像头#001C1-cam-09角度调整</div>
            </div>
            <div class="pb-3 border-b border-gray-100 dark:border-gray-700">
              <div class="text-sm">2025-10-10 11:55:33</div>
              <div class="text-gray-700 dark:text-gray-300 text-sm mt-1">增氧机#001C1-17参数调整</div>
            </div>
            <div class="pb-3 border-b border-gray-100 dark:border-gray-700">
              <div class="text-sm">2025-10-10 11:40:18</div>
              <div class="text-gray-700 dark:text-gray-300 text-sm mt-1">系统自动巡检完成</div>
            </div>
            <div class="pb-3">
              <div class="text-sm">2025-10-10 11:20:09</div>
              <div class="text-gray-700 dark:text-gray-300 text-sm mt-1">投喂计划更新</div>
            </div>
          </div>
        </section>

        <!-- 设备告警信息 -->
        <section class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-5 backdrop-blur">
          <h3 class="text-lg font-semibold mb-4 flex items-center">
            <i class="fa fa-exclamation-triangle mr-2 text-cyan-300"></i>设备告警信息
          </h3>
          <div class="space-y-4 max-h-[480px] overflow-y-auto pr-2">
            <div class="p-3 bg-red-50/40 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500 alert-pulse-red">
              <div class="font-medium">高温告警</div>
              <div class="text-sm text-white/75 mt-1">增氧机#001C1-17</div>
              <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-white/60">25分钟前</span>
                <span class="text-sm bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-300 px-2.5 py-0.5 rounded font-semibold">立即处理</span>
              </div>
            </div>
            <div class="p-3 bg-orange-50/40 dark:bg-orange-900/20 rounded-lg border-l-4 border-orange-500">
              <div class="font-medium">饲料不足</div>
              <div class="text-sm text-white/75 mt-1">投饵机#001C1-twj-12</div>
              <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-white/60">2分钟前</span>
                <span class="text-sm bg-orange-100 dark:bg-orange-800 text-orange-600 dark:text-orange-300 px-2.5 py-0.5 rounded font-medium">安排补充</span>
              </div>
            </div>
            <div class="p-3 bg-yellow-50/40 dark:bg-yellow-900/20 rounded-lg border-l-4 border-yellow-500">
              <div class="font-medium">水压异常</div>
              <div class="text-sm text-white/75 mt-1">水泵#001C1-p-03</div>
              <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-white/60">15分钟前</span>
                <span class="text-sm bg-yellow-100 dark:bg-yellow-800 text-yellow-600 dark:text-yellow-300 px-2.5 py-0.5 rounded font-medium">检查管道</span>
              </div>
            </div>
            <div class="p-3 bg-red-50/40 dark:bg-red-900/20 rounded-lg border-l-4 border-red-500">
              <div class="font-medium">设备离线</div>
              <div class="text-sm text-white/75 mt-1">摄像头#001C1-cam-09</div>
              <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-white/60">30分钟前</span>
                <span class="text-sm bg-red-100 dark:bg-red-800 text-red-600 dark:text-red-300 px-2.5 py-0.5 rounded font-medium">检查连线</span>
              </div>
            </div>
            <div class="p-3 bg-orange-50/40 dark:bg-orange-900/20 rounded-lg border-l-4 border-orange-500">
              <div class="font-medium">电量低</div>
              <div class="text-sm text-white/75 mt-1">自动水阀#001C1-v-07</div>
              <div class="flex justify-between items-center mt-2">
                <span class="text-xs text-white/60">1小时前</span>
                <span class="text-sm bg-orange-100 dark:bg-orange-800 text-orange-600 dark:text-orange-300 px-2.5 py-0.5 rounded font-medium">安排充电</span>
              </div>
            </div>
          </div>
        </section>

        <!-- 故障统计 -->
        <section class="bg-white/10 border border-white/10 rounded-lg shadow-sm p-5 backdrop-blur">
          <h3 class="text-lg font-semibold mb-4 flex items-center">
            <i class="fa fa-bar-chart mr-2 text-cyan-300"></i>故障统计
          </h3>
          <div class="space-y-5 max-h-[480px] overflow-y-auto pr-2">
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>投饵机</span>
                <span>14次 (10%)</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-orange-500 h-2 rounded-full" style="width: 10%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>摄像头</span>
                <span>17次 (5%)</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full" style="width: 5%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>增氧机</span>
                <span>23次 (13%)</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-orange-500 h-2 rounded-full" style="width: 13%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>水泵</span>
                <span>38次 (22%)</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full" style="width: 22%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>自动水阀</span>
                <span>12次 (8%)</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-orange-500 h-2 rounded-full" style="width: 8%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>传感器</span>
                <span>41次 (25%)</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-red-500 h-2 rounded-full" style="width: 25%"></div>
              </div>
            </div>
            <div>
              <div class="flex justify-between text-sm mb-1">
                <span>控制器</span>
                <span>21次 (17%)</span>
              </div>
              <div class="w-full bg-white/10 rounded-full h-2">
                <div class="bg-orange-500 h-2 rounded-full" style="width: 17%"></div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>

    
  </div>
</template>

<script setup>
// 这里可以添加交互逻辑，目前使用静态数据
</script>

<style scoped>
/* 自定义滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

/* 高温告警红色透明闪烁 */
@keyframes pulse-red-soft {
  0%, 100% {
    background-color: rgba(239, 68, 68, 0.25); /* red-500 @ 25% */
    border-color: rgba(239, 68, 68, 0.7);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.35);
  }
  50% {
    background-color: rgba(239, 68, 68, 0.12);
    border-color: rgba(239, 68, 68, 0.45);
    box-shadow: 0 0 0 8px rgba(239, 68, 68, 0.12);
  }
}

.alert-pulse-red {
  animation: pulse-red-soft 1.2s ease-in-out infinite;
}
</style>
