<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">门禁管理</h1>
      <p class="page-subtitle">园区门禁系统管理与控制</p>
    </div>
    
    <div class="page-content">
      <div class="content-grid">
        <!-- 门禁统计卡片 -->
        <div class="stat-card">
          <div class="stat-icon">
            <div class="i-mdi-door text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">12</div>
            <div class="stat-label">门禁点位</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon online">
            <div class="i-mdi-lock-open text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">11</div>
            <div class="stat-label">在线设备</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon users">
            <div class="i-mdi-account-group text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">156</div>
            <div class="stat-label">授权用户</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon records">
            <div class="i-mdi-history text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">89</div>
            <div class="stat-label">今日通行</div>
          </div>
        </div>
      </div>
      
      <!-- 门禁设备列表 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>门禁设备管理</h3>
          <div class="panel-actions">
            <button class="btn-primary">添加设备</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>设备编号</th>
                  <th>设备名称</th>
                  <th>位置</th>
                  <th>设备类型</th>
                  <th>状态</th>
                  <th>最后通信</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="device in accessDeviceList" :key="device.id">
                  <td>{{ device.code }}</td>
                  <td>{{ device.name }}</td>
                  <td>{{ device.location }}</td>
                  <td>{{ device.type }}</td>
                  <td>
                    <span class="status-badge" :class="device.status">
                      {{ device.statusText }}
                    </span>
                  </td>
                  <td>{{ device.lastCommunication }}</td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">开门</button>
                      <button class="btn-small">设置</button>
                      <button class="btn-small btn-info">记录</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- 通行记录 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>通行记录</h3>
          <div class="panel-actions">
            <button class="btn-secondary">导出记录</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>通行时间</th>
                  <th>用户姓名</th>
                  <th>卡号</th>
                  <th>门禁点</th>
                  <th>通行方向</th>
                  <th>通行状态</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="record in accessRecordList" :key="record.id">
                  <td>{{ record.time }}</td>
                  <td>{{ record.userName }}</td>
                  <td>{{ record.cardNumber }}</td>
                  <td>{{ record.accessPoint }}</td>
                  <td>
                    <span class="direction-badge" :class="record.direction">
                      {{ record.directionText }}
                    </span>
                  </td>
                  <td>
                    <span class="status-badge" :class="record.status">
                      {{ record.statusText }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface AccessDevice {
  id: number
  code: string
  name: string
  location: string
  type: string
  status: string
  statusText: string
  lastCommunication: string
}

interface AccessRecord {
  id: number
  time: string
  userName: string
  cardNumber: string
  accessPoint: string
  direction: string
  directionText: string
  status: string
  statusText: string
}

const accessDeviceList = ref<AccessDevice[]>([
  {
    id: 1,
    code: 'AC001',
    name: '主入口门禁',
    location: '园区大门',
    type: '刷卡门禁',
    status: 'online',
    statusText: '在线',
    lastCommunication: '2024-08-21 15:30'
  },
  {
    id: 2,
    code: 'AC002',
    name: '办公楼门禁',
    location: '办公楼入口',
    type: '人脸识别',
    status: 'online',
    statusText: '在线',
    lastCommunication: '2024-08-21 15:28'
  },
  {
    id: 3,
    code: 'AC003',
    name: '仓库门禁',
    location: '仓库入口',
    type: '指纹门禁',
    status: 'offline',
    statusText: '离线',
    lastCommunication: '2024-08-21 12:15'
  }
])

const accessRecordList = ref<AccessRecord[]>([
  {
    id: 1,
    time: '2024-08-21 15:25',
    userName: '张三',
    cardNumber: '0001234567',
    accessPoint: '主入口门禁',
    direction: 'in',
    directionText: '进入',
    status: 'success',
    statusText: '成功'
  },
  {
    id: 2,
    time: '2024-08-21 15:20',
    userName: '李四',
    cardNumber: '0001234568',
    accessPoint: '办公楼门禁',
    direction: 'out',
    directionText: '离开',
    status: 'success',
    statusText: '成功'
  },
  {
    id: 3,
    time: '2024-08-21 15:15',
    userName: '未知用户',
    cardNumber: '0001234999',
    accessPoint: '仓库门禁',
    direction: 'in',
    directionText: '进入',
    status: 'failed',
    statusText: '失败'
  }
])
</script>

<style scoped>
.page-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(102, 204, 255, 0.1);
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 204, 255, 0.2);
  color: #66ccff;
}

.stat-icon.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-icon.users {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.stat-icon.records {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #66ccff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(102, 204, 255, 0.7);
}

.data-panel {
  background: rgba(102, 204, 255, 0.05);
  border: 1px solid rgba(102, 204, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #66ccff;
}

.btn-primary, .btn-secondary {
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #66ccff, #33aaff);
  color: #001122;
}

.btn-secondary {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border: 1px solid rgba(102, 204, 255, 0.3);
}

.btn-primary:hover, .btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 204, 255, 0.3);
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(102, 204, 255, 0.1);
}

.data-table th {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  font-weight: 600;
}

.data-table td {
  color: rgba(102, 204, 255, 0.9);
}

.status-badge, .direction-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-badge.success {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.failed {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.direction-badge.in {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.direction-badge.out {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 12px;
  border: 1px solid rgba(102, 204, 255, 0.3);
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: rgba(102, 204, 255, 0.2);
}

.btn-small.btn-info {
  border-color: rgba(168, 85, 247, 0.3);
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.btn-small.btn-info:hover {
  background: rgba(168, 85, 247, 0.2);
}
</style>
