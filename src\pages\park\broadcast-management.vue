<template>
  <div class="page-container">
    <div class="page-header">
      <h1 class="page-title">广播管理</h1>
      <p class="page-subtitle">园区广播系统管理与控制</p>
    </div>
    
    <div class="page-content">
      <div class="content-grid">
        <!-- 广播统计卡片 -->
        <div class="stat-card">
          <div class="stat-icon">
            <div class="i-mdi-bullhorn text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">18</div>
            <div class="stat-label">广播终端</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon online">
            <div class="i-mdi-volume-high text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">16</div>
            <div class="stat-label">在线终端</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon schedule">
            <div class="i-mdi-clock text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">5</div>
            <div class="stat-label">定时任务</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon playing">
            <div class="i-mdi-play text-3xl"></div>
          </div>
          <div class="stat-info">
            <div class="stat-value">2</div>
            <div class="stat-label">正在播放</div>
          </div>
        </div>
      </div>
      
      <!-- 广播控制面板 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>广播控制</h3>
          <div class="panel-actions">
            <button class="btn-primary">全区广播</button>
            <button class="btn-secondary">紧急广播</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="broadcast-control">
            <div class="control-section">
              <h4>快速广播</h4>
              <div class="control-buttons">
                <button class="control-btn">上班提醒</button>
                <button class="control-btn">下班提醒</button>
                <button class="control-btn">安全提醒</button>
                <button class="control-btn">天气预报</button>
              </div>
            </div>
            <div class="control-section">
              <h4>音量控制</h4>
              <div class="volume-control">
                <span>主音量</span>
                <input type="range" min="0" max="100" value="75" class="volume-slider">
                <span>75%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 广播终端列表 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>广播终端管理</h3>
          <div class="panel-actions">
            <button class="btn-primary">添加终端</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>终端编号</th>
                  <th>终端名称</th>
                  <th>位置</th>
                  <th>状态</th>
                  <th>音量</th>
                  <th>最后通信</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="terminal in terminalList" :key="terminal.id">
                  <td>{{ terminal.code }}</td>
                  <td>{{ terminal.name }}</td>
                  <td>{{ terminal.location }}</td>
                  <td>
                    <span class="status-badge" :class="terminal.status">
                      {{ terminal.statusText }}
                    </span>
                  </td>
                  <td>{{ terminal.volume }}%</td>
                  <td>{{ terminal.lastCommunication }}</td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">播放</button>
                      <button class="btn-small">停止</button>
                      <button class="btn-small btn-info">设置</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
      
      <!-- 定时任务 -->
      <div class="data-panel">
        <div class="panel-header">
          <h3>定时广播任务</h3>
          <div class="panel-actions">
            <button class="btn-primary">新建任务</button>
          </div>
        </div>
        <div class="panel-content">
          <div class="table-container">
            <table class="data-table">
              <thead>
                <tr>
                  <th>任务名称</th>
                  <th>播放时间</th>
                  <th>播放内容</th>
                  <th>播放区域</th>
                  <th>状态</th>
                  <th>操作</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="task in taskList" :key="task.id">
                  <td>{{ task.name }}</td>
                  <td>{{ task.time }}</td>
                  <td>{{ task.content }}</td>
                  <td>{{ task.area }}</td>
                  <td>
                    <span class="status-badge" :class="task.status">
                      {{ task.statusText }}
                    </span>
                  </td>
                  <td>
                    <div class="action-buttons">
                      <button class="btn-small">编辑</button>
                      <button class="btn-small btn-success">启用</button>
                      <button class="btn-small btn-danger">删除</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface BroadcastTerminal {
  id: number
  code: string
  name: string
  location: string
  status: string
  statusText: string
  volume: number
  lastCommunication: string
}

interface BroadcastTask {
  id: number
  name: string
  time: string
  content: string
  area: string
  status: string
  statusText: string
}

const terminalList = ref<BroadcastTerminal[]>([
  {
    id: 1,
    code: 'BC001',
    name: '主入口广播',
    location: '园区大门',
    status: 'online',
    statusText: '在线',
    volume: 75,
    lastCommunication: '2024-08-21 15:30'
  },
  {
    id: 2,
    code: 'BC002',
    name: '办公区广播',
    location: '办公楼',
    status: 'playing',
    statusText: '播放中',
    volume: 60,
    lastCommunication: '2024-08-21 15:29'
  },
  {
    id: 3,
    code: 'BC003',
    name: '养殖区广播',
    location: '养殖池区域',
    status: 'offline',
    statusText: '离线',
    volume: 0,
    lastCommunication: '2024-08-21 12:15'
  }
])

const taskList = ref<BroadcastTask[]>([
  {
    id: 1,
    name: '上班提醒',
    time: '每日 08:00',
    content: '上班时间到，请各位员工准时到岗',
    area: '全区',
    status: 'active',
    statusText: '启用'
  },
  {
    id: 2,
    name: '午休提醒',
    time: '每日 12:00',
    content: '午休时间，请注意休息',
    area: '办公区',
    status: 'active',
    statusText: '启用'
  },
  {
    id: 3,
    name: '下班提醒',
    time: '每日 18:00',
    content: '下班时间到，请注意安全',
    area: '全区',
    status: 'inactive',
    statusText: '禁用'
  }
])
</script>

<style scoped>
.page-container {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
  background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 32px;
  font-weight: bold;
  color: #66ccff;
  text-shadow: 0 0 20px rgba(102, 204, 255, 0.8);
  margin-bottom: 8px;
}

.page-subtitle {
  font-size: 16px;
  color: rgba(102, 204, 255, 0.7);
}

.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: rgba(102, 204, 255, 0.1);
  border: 1px solid rgba(102, 204, 255, 0.3);
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  backdrop-filter: blur(10px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(102, 204, 255, 0.2);
  color: #66ccff;
}

.stat-icon.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.stat-icon.schedule {
  background: rgba(251, 191, 36, 0.2);
  color: #fbbf24;
}

.stat-icon.playing {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  color: #66ccff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: rgba(102, 204, 255, 0.7);
}

.data-panel {
  background: rgba(102, 204, 255, 0.05);
  border: 1px solid rgba(102, 204, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  margin-bottom: 24px;
}

.panel-header {
  padding: 20px 24px;
  border-bottom: 1px solid rgba(102, 204, 255, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #66ccff;
}

.panel-actions {
  display: flex;
  gap: 12px;
}

.btn-primary, .btn-secondary {
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background: linear-gradient(135deg, #66ccff, #33aaff);
  color: #001122;
}

.btn-secondary {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
}

.btn-primary:hover, .btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 204, 255, 0.3);
}

.broadcast-control {
  padding: 24px;
}

.control-section {
  margin-bottom: 24px;
}

.control-section h4 {
  color: #66ccff;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
}

.control-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.control-btn {
  background: rgba(102, 204, 255, 0.1);
  border: 1px solid rgba(102, 204, 255, 0.3);
  color: #66ccff;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: rgba(102, 204, 255, 0.2);
  transform: translateY(-2px);
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 16px;
  color: rgba(102, 204, 255, 0.9);
}

.volume-slider {
  flex: 1;
  max-width: 200px;
  height: 6px;
  background: rgba(102, 204, 255, 0.2);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  background: #66ccff;
  border-radius: 50%;
  cursor: pointer;
}

.table-container {
  overflow-x: auto;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th,
.data-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid rgba(102, 204, 255, 0.1);
}

.data-table th {
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  font-weight: 600;
}

.data-table td {
  color: rgba(102, 204, 255, 0.9);
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.status-badge.online {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.offline {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.status-badge.playing {
  background: rgba(168, 85, 247, 0.2);
  color: #a855f7;
}

.status-badge.active {
  background: rgba(34, 197, 94, 0.2);
  color: #22c55e;
}

.status-badge.inactive {
  background: rgba(156, 163, 175, 0.2);
  color: #9ca3af;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-small {
  padding: 4px 12px;
  border: 1px solid rgba(102, 204, 255, 0.3);
  background: rgba(102, 204, 255, 0.1);
  color: #66ccff;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-small:hover {
  background: rgba(102, 204, 255, 0.2);
}

.btn-small.btn-info {
  border-color: rgba(168, 85, 247, 0.3);
  background: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

.btn-small.btn-success {
  border-color: rgba(34, 197, 94, 0.3);
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.btn-small.btn-danger {
  border-color: rgba(239, 68, 68, 0.3);
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.btn-small.btn-info:hover {
  background: rgba(168, 85, 247, 0.2);
}

.btn-small.btn-success:hover {
  background: rgba(34, 197, 94, 0.2);
}

.btn-small.btn-danger:hover {
  background: rgba(239, 68, 68, 0.2);
}
</style>
